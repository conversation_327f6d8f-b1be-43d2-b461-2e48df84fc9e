package vn.com.bidv.feature.government.service.data

import vn.com.bidv.feature.common.data.masterdata.model.InqCustomerCriteriaDto
import vn.com.bidv.feature.government.service.data.governmentservice.model.InquiryCustomsDutyReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.Page
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnPendingListReq
import vn.com.bidv.feature.government.service.domain.model.DataListTxnPendingDMO
import vn.com.bidv.feature.government.service.domain.model.TaxPayerInfoDMO
import vn.com.bidv.feature.government.service.domain.model.DataListInquiryCustomsDutyDMO
import vn.com.bidv.feature.government.service.domain.model.InquiryCustomsDutyDMO
import vn.com.bidv.feature.government.service.model.ListCustomsDutiesRuleFilter
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.TransactionStatusBase
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants
import vn.com.bidv.sdkbase.utils.convert
import vn.com.bidv.sdkbase.utils.exts.formatDateToString
import javax.inject.Inject

class GovernmentServiceUseCase @Inject constructor(
    private val governmentServiceRepository: GovernmentServiceRepository
) {

    suspend fun getListPendingTransaction(
        rule: ListCustomsDutiesRuleFilter?,
        pageIndex: Int,
        pageSize: Int
    ): DomainResult<DataListTxnPendingDMO> {

        val request = TxnPendingListReq(
            page = Page(
                pageNum = pageIndex,
                pageSize = pageSize
            ),
            search = rule?.search,
            startDate = rule?.startDate?.formatDateToString(SdkBaseConstants.DateTimeConstants.FORMAT_YYYY_MM_DD),
            endDate = rule?.endDate?.formatDateToString(SdkBaseConstants.DateTimeConstants.FORMAT_YYYY_MM_DD),
            minAmount = rule?.minAmount,
            maxAmount = rule?.maxAmount,
            ccy = "VND",
            statuses = rule?.listStatusSelected,
            debitAccNo = rule?.debit,
            taxCode = rule?.tax,
            declarationNo = rule?.declaration,
            batchNo = rule?.batch
        )

        val result = governmentServiceRepository.getListPendingTransaction(request)
        return result.convert(DataListTxnPendingDMO::class.java)
    }

    fun getListCurrency(): DomainResult<List<String>> {
        return DomainResult.Success(listOf("VND"))
    }

    fun getListStatus(): DomainResult<List<TransactionStatusBase>> {
        return DomainResult.Success(
            listOf(TransactionStatusBase.INIT, TransactionStatusBase.REJECTED)
        )
    }

    suspend fun inquiryTransaction(taxId: String, declarationNo: String?, year: Int?): DomainResult<DataListInquiryCustomsDutyDMO> {
        val result = governmentServiceRepository.inquiryTransaction(
            InquiryCustomsDutyReq(
                taxCode = taxId,
                declarationNo = declarationNo,
                declarationYear = year?.toString()
            )
        )
        return result.convert {
            DataListInquiryCustomsDutyDMO(
                items = this.items!!.map { InquiryCustomsDutyDMO.fromRes(it) }
            )
        }
    }

    suspend fun getTaxPayerInformation(cifNo: String): DomainResult<TaxPayerInfoDMO> {
        val result = governmentServiceRepository.getTaxPayerInformation(
            InqCustomerCriteriaDto(
                cifNo = cifNo
            )
        )
        return result.convert {
            TaxPayerInfoDMO(
                taxCode = taxCode ?: "",
                payerName = name ?: "",
                payerAddress = address ?: "",
            )
        }
    }

    fun getListChapterCode(): List<String> {
        return listOf("Mã chương 1", "Mã chương 2", "Mã chương 3", "Mã chương 4", "Mã chương 5")
    }

    fun getListEconomicCode(): List<String> {
        return listOf(
            "Mã nội dung kinh tế 1",
            "Mã nội dung kinh tế 2",
            "Mã nội dung kinh tế 3",
            "Mã nội dung kinh tế 4",
        )
    }

    fun getListCurrencyType(): List<String> {
        return listOf("Loại tiền 1", "Loại tiền 2", "Loại tiền 3", "Loại tiền 4", "Loại tiền 5")
    }

    fun getListTaxType(): List<String> {
        return listOf("Sắc thuế 1", "Sắc thuế 2", "Sắc thuế 3", "Sắc thuế 4", "Sắc thuế 5")
    }

    fun getListCustomsCurrency(): List<String> {
        return listOf("Loại tiền HQ 1", "Loại tiền HQ 2", "Loại tiền HQ 3", "Loại tiền HQ 4")
    }

    fun getListTradeType(): List<String> {
        return listOf("Loại hình XNK 1", "Loại hình XNK 2", "Loại hình XNK 3", "Loại hình XNK 4")
    }
}