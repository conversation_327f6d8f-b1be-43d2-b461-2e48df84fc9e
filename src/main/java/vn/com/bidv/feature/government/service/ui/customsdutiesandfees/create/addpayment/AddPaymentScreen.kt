package vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.AddPaymentDatePicker
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.ShowDatePickerBottomSheet
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown.ChapterCodeDropDown
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown.CurrencyTypeDropDown
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown.CustomsCurrencyDropDown
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown.EconomicCodeDropDown
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown.ShowChapterCodeBottomSheet
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown.ShowCurrencyTypeBottomSheet
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown.ShowCustomsCurrencyBottomSheet
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown.ShowEconomicCodeBottomSheet
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown.ShowTaxTypeBottomSheet
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown.ShowTradeTypeBottomSheet
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown.TaxTypeDropDown
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown.TradeTypeDropDown

@Composable
fun AddPaymentScreen(
    navController: NavHostController
) {

    val listAddPaymentItems: List<AddPaymentItem> = listOf(
        AddPaymentItem.DeclarationNumber,
        AddPaymentItem.Date,
        AddPaymentItem.DropDownItem.ChapterCode,
        AddPaymentItem.DropDownItem.EconomicCode,
        AddPaymentItem.DropDownItem.CurrencyType,
        AddPaymentItem.InputMoney,
        AddPaymentItem.TransactionDescription,
        AddPaymentItem.DropDownItem.TaxType,
        AddPaymentItem.DropDownItem.CustomsCurrency,
        AddPaymentItem.DropDownItem.TradeType,
    )

    val viewModel: AddPaymentViewModel = hiltViewModel()
    val colorScheme = LocalColorScheme.current

    val uiState by viewModel.uiState.collectAsState()

    LaunchedEffect(uiState.isValid) {
        if(uiState.isValid) {
            navController.popBackStack()
        }
    }


    BaseScreen(
        navController = navController,
        viewModel = viewModel,
        topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(vn.com.bidv.localization.R.string.them_khoan_nop),
            showHomeIcon = false
        ),
        renderContent = { uiState, onEvent ->

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(colorScheme.bgMainPrimary)
            ) {
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                        .padding(
                            start = IBSpacing.spacingM,
                            end = IBSpacing.spacingM,
                            bottom = IBSpacing.spacingM
                        )
                ) {
                    AddPaymentScreenContent(
                        navController = navController,
                        viewModel = viewModel,
                        uiState = uiState,
                        onEvent = onEvent,
                        listAddPaymentItems = listAddPaymentItems,
                        onValueChange = { field, value ->
                            onEvent(
                                AddPaymentReducer.AddPaymentEvent.ValueTextChange(
                                    declarationNumber = if (field is AddPaymentItem.DeclarationNumber) value else null,
                                    amount = if (field is AddPaymentItem.InputMoney) value else null,
                                    transactionDescription = if (field is AddPaymentItem.TransactionDescription) value else null,
                                )
                            )
                        }
                    )
                }

                IBankNormalButton(
                    modifier = Modifier
                        .wrapContentHeight()
                        .fillMaxWidth()
                        .background(LocalColorScheme.current.bgMainTertiary)
                        .padding(16.dp),
                    text = stringResource(vn.com.bidv.localization.R.string.luu)
                ) {
                    onEvent(AddPaymentReducer.AddPaymentEvent.ValidateField)
                }

            }
        }
    )
}

@Composable
fun AddPaymentScreenContent(
    navController: NavHostController,
    viewModel: AddPaymentViewModel,
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
    listAddPaymentItems: List<AddPaymentItem>,
    onValueChange: (field: AddPaymentItem, value: String) -> Unit = { _, _ -> },
) {

    val showDatePicker = remember { mutableStateOf(false) }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingM)
    ) {
        items(listAddPaymentItems.size) { index ->
            when (listAddPaymentItems[index]) {
                is AddPaymentItem.DeclarationNumber -> DeclarationNumberInputField(
                    uiState = uiState,
                    onValueChange = onValueChange
                )

                is AddPaymentItem.InputMoney -> AmountInputField(
                    uiState = uiState,
                    onValueChange = onValueChange
                )

                is AddPaymentItem.TransactionDescription -> TransactionDescriptionInputField(
                    uiState = uiState,
                    onValueChange = onValueChange
                )

                is AddPaymentItem.Date -> AddPaymentDatePicker(
                    uiState = uiState,
                    onEvent = onEvent,
                    showDatePicker = showDatePicker
                )

                is AddPaymentItem.DropDownItem.ChapterCode -> ChapterCodeDropDown(
                    uiState = uiState,
                    onEvent = onEvent,
                )

                is AddPaymentItem.DropDownItem.EconomicCode -> EconomicCodeDropDown(
                    uiState = uiState,
                    onEvent = onEvent,
                )

                is AddPaymentItem.DropDownItem.CurrencyType -> CurrencyTypeDropDown(
                    uiState = uiState,
                    onEvent = onEvent
                )

                is AddPaymentItem.DropDownItem.TaxType -> TaxTypeDropDown(
                    uiState = uiState,
                    onEvent = onEvent
                )

                is AddPaymentItem.DropDownItem.CustomsCurrency -> CustomsCurrencyDropDown(
                    uiState = uiState,
                    onEvent = onEvent
                )

                is AddPaymentItem.DropDownItem.TradeType -> TradeTypeDropDown(
                    uiState = uiState,
                    onEvent = onEvent
                )
            }
        }
    }

    ShowDatePickerBottomSheet(onEvent = onEvent, showDatePicker = showDatePicker)

    ShowChapterCodeBottomSheet(uiState = uiState, onEvent = onEvent)

    ShowEconomicCodeBottomSheet(uiState = uiState, onEvent = onEvent)

    ShowCurrencyTypeBottomSheet(uiState = uiState, onEvent = onEvent)

    ShowTaxTypeBottomSheet(uiState = uiState, onEvent = onEvent)

    ShowCustomsCurrencyBottomSheet(uiState = uiState, onEvent = onEvent)

    ShowTradeTypeBottomSheet(uiState = uiState, onEvent = onEvent)
}
