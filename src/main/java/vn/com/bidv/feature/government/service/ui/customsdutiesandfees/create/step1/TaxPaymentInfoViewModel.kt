package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step1

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.government.service.data.GovernmentServiceUseCase
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class TaxPaymentInfoViewModel @Inject constructor(
    private val governmentServiceUseCase: GovernmentServiceUseCase,
    private val userInfoUseCase: UserInfoUseCase,
) :
    ViewModelIBankBase<
            TaxPaymentInfoReducer.TaxPaymentViewState,
            TaxPaymentInfoReducer.TaxPaymentViewEvent,
            TaxPaymentInfoReducer.TaxPaymentSideEffect
            >(
        initialState = TaxPaymentInfoReducer.TaxPaymentViewState(),
        reducer = TaxPaymentInfoReducer()
    ) {
    override fun handleEffect(
        sideEffect: TaxPaymentInfoReducer.TaxPaymentSideEffect,
        onResult: (TaxPaymentInfoReducer.TaxPaymentViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is TaxPaymentInfoReducer.TaxPaymentSideEffect.InitScreen -> {
                callDomain(
                    onSuccess = {
                        val (taxPayerInfo, transactions) = it.data!!
                        onResult(
                            TaxPaymentInfoReducer.TaxPaymentViewEvent.InitScreenSuccess(
                                taxPayerInfo = taxPayerInfo,
                                transactions = transactions,
                            )
                        )
                    }
                ) {
                    val cifNo =
                        userInfoUseCase.getUserInfoFromStorage().getSafeData()?.user?.cifNo ?: ""
                    val taxPayerInfo =
                        governmentServiceUseCase.getTaxPayerInformation(cifNo).getSafeData()!!
                    val transactions = governmentServiceUseCase.inquiryTransaction(
                        taxId = "0100100079", // TODO: remove hardcode
                        declarationNo = null,
                        year = null,
                    ).getSafeData()!!.items
                    DomainResult.Success(taxPayerInfo to transactions)
                }
            }
        }
    }
}
