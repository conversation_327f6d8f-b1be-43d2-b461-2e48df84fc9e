package vn.com.bidv.feature.government.service.ui.common

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMoreReducer
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.RuleFilters
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ItemCardHeaderCommon
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.model.ActionType

@Composable
fun <T, R : RuleFilters> ItemCardCommonWithModifier(
    modifier: Modifier = Modifier,
    transactionsDMO: ModelCheckAble<T>,
    uiState: ViewState?,
    onEventListView: ((ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent<T, R>) -> Unit)?,
    onClickItem: () -> Unit = {},
    listAction: List<ActionType> = emptyList(),
    shouldShowItemCheckBox: Boolean = true,
    headerActionIconId: Int = vn.com.bidv.designsystem.R.drawable.more_vertical_filled,
    handleAction: (ActionType) -> Unit,
    contentHeader: @Composable (ModelCheckAble<T>) -> Unit = {},
    contentBody: @Composable (ModelCheckAble<T>) -> Unit = {},
) {
    val colorScheme = LocalColorScheme.current
    Box(
        modifier
            .clip(RoundedCornerShape(IBCornerRadius.cornerRadiusL))
            .background(color = colorScheme.bgMainTertiary)
    ) {
        Column(
            Modifier
                .clickable {
                    onClickItem()
                }) {
            ItemCardHeaderCommon(
                transactionsDMO,
                uiState,
                onEventListView,
                shouldShowItemCheckBox = shouldShowItemCheckBox,
                headerActionIconId = headerActionIconId,
                contentHeader = contentHeader,
                listAction = listAction,
                handleAction = handleAction
            )
            HorizontalDivider(
                color = colorScheme.borderMainSecondary, thickness = IBBorderDivider.borderDividerS
            )
            Spacer(Modifier.padding(top = IBSpacing.spacingXs))
            contentBody(transactionsDMO)
        }
    }
}