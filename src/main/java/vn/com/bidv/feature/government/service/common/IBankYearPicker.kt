package vn.com.bidv.feature.government.service.common

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.component.datepicker.model.DatePickerConfig
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonSize
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.R as RDesignSystem
import vn.com.bidv.localization.R as RLocalization

@Composable
fun IBankYearPicker(
    modifier: Modifier = Modifier,
    title: String = stringResource(RLocalization.string.chon_nam),
    startYear: Int,
    selectedYear: Int,
    negativeButtonText: String = stringResource(RLocalization.string.huy),
    positiveButtonText: String = stringResource(RLocalization.string.xac_nhan),
    config: DatePickerConfig = DatePickerConfig.build(),
    onClickSubmit: (year: Int) -> Unit = {},
    onCancel: () -> Unit = {},
    onClose: () -> Unit = {},
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current

    var selectedYearState by remember { mutableIntStateOf(selectedYear) }

    Column(modifier = modifier) {
        // Bottom sheet title and close button
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = IBSpacing.spacingM, vertical = IBSpacing.spacingS),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                style = typography.titleTitle_l,
                color = colorScheme.contentMainPrimary
            )

            IconButton(
                onClick = onClose,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = ImageVector.vectorResource(id = RDesignSystem.drawable.close_outline),
                    contentDescription = null,
                    modifier = Modifier.size(20.dp),
                    tint = colorScheme.contentMainPrimary
                )
            }
        }

        // Year Picker content
        YearPicker(
            modifier = Modifier
                .testTagIBank("IBankYearPicker_YearPicker")
                .fillMaxWidth(),
            startYear = startYear,
            selected = selectedYear,
            datePickerConfig = config,
        ) { yearSelected ->
            selectedYearState = yearSelected
        }

        // CTAs
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IBankNormalButton(
                text = negativeButtonText,
                size = NormalButtonSize.L(typography),
                type = NormalButtonType.SECONDARYGRAY(colorScheme),
                onClick = onCancel,
                modifier = Modifier.weight(1f),
            )

            Spacer(modifier = Modifier.width(16.dp))

            IBankNormalButton(
                text = positiveButtonText,
                size = NormalButtonSize.L(typography),
                type = NormalButtonType.PRIMARY(colorScheme),
                onClick = {
                    onClickSubmit(selectedYearState)
                },
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun IBankYearPickerDialog(
    modifier: Modifier = Modifier,
    title: String = stringResource(RLocalization.string.chon_nam),
    selectedYear: Int,
    negativeButtonText: String = stringResource(RLocalization.string.huy),
    positiveButtonText: String = stringResource(RLocalization.string.xac_nhan),
    config: DatePickerConfig = DatePickerConfig.build(),
    onDismissRequest: () -> Unit = { },
    onClickSubmit: (year: Int) -> Unit = {},
    onNegativeAction: () -> Unit = {},
) {
    val sheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true
    )

    ModalBottomSheet(
        onDismissRequest = onDismissRequest,
        sheetState = sheetState,
        dragHandle = null,
    ) {
        IBankYearPicker(
            modifier = modifier.testTagIBank("IBankYearPickerDialog_$title"),
            title = title,
            startYear = selectedYear - 7,
            selectedYear = selectedYear,
            negativeButtonText = negativeButtonText,
            positiveButtonText = positiveButtonText,
            config = config,
            onClickSubmit = { year ->
                onClickSubmit(year)
                onDismissRequest()
            },
            onCancel = onNegativeAction,
            onClose = onDismissRequest,
        )
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewIBankYearPicker() {
    IBankYearPicker(
        modifier = Modifier,
        startYear = 2025 - 7,
        selectedYear = 2025,
        config = DatePickerConfig.build { },
        onClickSubmit = { },
        onCancel = { },
        onClose = { }
    )
}
