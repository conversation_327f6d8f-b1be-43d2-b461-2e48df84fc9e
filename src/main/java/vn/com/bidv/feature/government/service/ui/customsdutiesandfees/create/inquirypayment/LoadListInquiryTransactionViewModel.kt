package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.inquirypayment

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMoreReducer
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMoreViewModel
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.feature.government.service.data.GovernmentServiceUseCase
import vn.com.bidv.feature.government.service.domain.model.InquiryCustomsDutyDMO
import vn.com.bidv.sdkbase.domain.DomainResult
import javax.inject.Inject

@HiltViewModel
class LoadListInquiryTransactionViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    reducer: ListAutoLoadMoreReducer<InquiryCustomsDutyDMO, InquiryTransactionRuleFilter>,
    private val governmentServiceUseCase: GovernmentServiceUseCase
) : ListAutoLoadMoreViewModel<InquiryCustomsDutyDMO, InquiryTransactionRuleFilter>(
    reducer = reducer,
    itemPerPage = 20
) {

    var taxNumber: String = ""

    override fun fetchData(
        pageIndex: Int,
        pageSize: Int,
        rule: InquiryTransactionRuleFilter?,
        onLoadSuccess: (data: List<InquiryCustomsDutyDMO>, total: Int?) -> Unit,
        onLoadFail: (String?) -> Unit
    ): Job {
        return viewModelScope.launch(dispatcher) {
            // Only fetch data if we have the required parameters
            if (rule?.taxDeclarationNo?.isNotEmpty() == true && rule.taxDeclarationYear != null && taxNumber.isNotEmpty()) {
                when (val result = governmentServiceUseCase.inquiryTransaction(
                    taxId = "0100100079", // TODO: remove hardcode
                    declarationNo = rule.taxDeclarationNo,
                    year = rule.taxDeclarationYear
                )) {
                    is DomainResult.Success -> {
                        result.data?.let {
                            onLoadSuccess(
                                it.items,
                                it.items.size
                            )
                        } ?: onLoadSuccess(emptyList(), 0)
                    }

                    is DomainResult.Error -> {
                        onLoadFail(result.errorMessage)
                    }
                }
            } else {
                // Return empty list if parameters are not set
                onLoadSuccess(emptyList(), 0)
            }
        }
    }
}
