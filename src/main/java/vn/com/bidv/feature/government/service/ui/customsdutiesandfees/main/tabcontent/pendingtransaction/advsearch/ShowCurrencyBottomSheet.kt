package vn.com.bidv.feature.government.service.ui.list.tabcontent.pendingtransaction.advsearch

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankMultiChoiceSearchDialog
import vn.com.bidv.designsystem.component.feedback.bottomsheet.ItemMultiSelect
import vn.com.bidv.designsystem.component.feedback.bottomsheet.MultiSearchDialogState
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.VNCharacterUtil

@Composable
fun ShowCurrencyBottomSheet(
    uiState: AdvSearchReducer.AdvSearchState,
    showCurrencyBottomSheet: MutableState<Boolean>,
    data: List<String>?,
    state: MultiSearchDialogState,
    onItemSelected: (List<String>) -> Unit,
) {
    if (showCurrencyBottomSheet.value) {
        val colorScheme = LocalColorScheme.current
        var listCurrencySelected by remember {
            mutableStateOf(
                uiState.listCurrencySelected ?: emptyList()
            )
        }

        LaunchedEffect(uiState.listCurrencySelected) {
            listCurrencySelected = uiState.listCurrencySelected ?: emptyList()
        }

        val totalSelected = listCurrencySelected.size
        val totalItems = data?.size ?: 0
        val isAllSelected = totalSelected == totalItems
        val isPartialSelected = totalSelected in 1 until totalItems

        IBankMultiChoiceSearchDialog(title = stringResource(R.string.loai_tien),
            searchPlaceholder = stringResource(R.string.tim_kiem),
            listData = data,
            showSearchBox = true,
            itemSelected = listCurrencySelected.ifEmpty { data },
            isSelectedAllSupport = true,
            state = state,
            onApply = {
                showCurrencyBottomSheet.value = false
                it?.let(onItemSelected)
            },
            searchFilter = { currency, query ->
                query.isBlank() || VNCharacterUtil.removeAccent(currency).contains(
                    VNCharacterUtil.removeAccent(query), ignoreCase = true
                )
            },
            selectedAllView = { _, click ->
                Column {
                    ItemMultiSelect(
                        isSelected = isAllSelected || isPartialSelected,
                        indeterminate = isPartialSelected,
                        content = stringResource(id = R.string.tat_ca)
                    ) {
                        listCurrencySelected = if (isAllSelected) {
                            emptyList()
                        } else {
                            data ?: emptyList()
                        }
                        click.invoke()
                    }
                    HorizontalDivider(
                        modifier = Modifier.padding(
                            bottom = IBSpacing.spacing2xs, top = IBSpacing.spacingXs
                        ),
                        thickness = IBBorderDivider.borderDividerS,
                        color = colorScheme.borderMainSecondary
                    )
                }
            },
            applyView = { click ->
                IBankActionBar(isVertical = false,
                    buttonNegative = DialogButtonInfo(stringResource(R.string.huy)) {
                        showCurrencyBottomSheet.value = false
                    },
                    typeNegativeButton = NormalButtonType.SECONDARYGRAY(colorScheme),
                    buttonPositive = DialogButtonInfo(stringResource(R.string.xac_nhan)) {
                        click.invoke()
                        showCurrencyBottomSheet.value = false
                    }
                )
            },
            itemContent = { _, item, click ->
                ItemMultiSelect(item.isSelected, item.data) {
                    val updatedList = listCurrencySelected.toMutableList()
                    item.data.let {
                        if (item.isSelected) {
                            updatedList.remove(it)
                        } else {
                            updatedList.add(it)
                        }
                    }
                    listCurrencySelected = updatedList
                    click.invoke()
                }
            }
        )
    }
}
