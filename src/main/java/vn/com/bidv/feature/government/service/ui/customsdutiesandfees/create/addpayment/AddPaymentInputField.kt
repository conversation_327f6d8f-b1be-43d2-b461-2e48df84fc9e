package vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankInputFieldBase
import vn.com.bidv.designsystem.component.dataentry.IBankInputMoney
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.feature.government.service.common.Constants

@Composable
fun DeclarationNumberInputField(
    uiState: AddPaymentReducer.AddPaymentState,
    onValueChange: (field: AddPaymentItem, value: String) -> Unit
) {

    val messageError = uiState.fieldError?.get(AddPaymentItem.DeclarationNumber::class.java) ?: ""
    val state = if (messageError != "") {
        IBFrameState.ERROR(LocalColorScheme.current)
    } else {
        IBFrameState.DEFAULT(LocalColorScheme.current)
    }

    IBankInputFieldBase(
        state = state,
        text = uiState.declarationNumber ?: "",
        placeholderText = "Số tờ khai/QĐ/TB",
        required = true,
        maxLengthText = Constants.MAX_LENGTH_TEXT_INPUT_20,
        onClickClear = {
            onValueChange(AddPaymentItem.DeclarationNumber, "")
        },
        helpTextLeft = messageError,
    ) { textFieldValue ->
        onValueChange(AddPaymentItem.DeclarationNumber, textFieldValue.text)
    }
}

@Composable
fun AmountInputField(
    uiState: AddPaymentReducer.AddPaymentState,
    onValueChange: (field: AddPaymentItem, value: String) -> Unit
) {

    val messageError = uiState.fieldError?.get(AddPaymentItem.InputMoney::class.java) ?: ""
    val state = if (messageError != "") {
        IBFrameState.ERROR(LocalColorScheme.current)
    } else {
        IBFrameState.DEFAULT(LocalColorScheme.current)
    }

    IBankInputMoney(
        state = state,
        textValue = uiState.amount ?: "",
        placeholderText = stringResource(vn.com.bidv.localization.R.string.so_tien),
        required = true,
        maxLengthText = Constants.MAX_LENGTH_TEXT_INPUT_16,
        onClickClear = {
            onValueChange(AddPaymentItem.InputMoney, "")
        },
        helpTextLeft = messageError,
        onAmountChange = { value ->
            onValueChange(AddPaymentItem.InputMoney, value)
        },
        onSelectCurrency = {},
        showCurrency = false
    )
}

@Composable
fun TransactionDescriptionInputField(
    uiState: AddPaymentReducer.AddPaymentState,
    onValueChange: (field: AddPaymentItem, value: String) -> Unit
) {

    val messageError = uiState.fieldError?.get(AddPaymentItem.TransactionDescription::class.java) ?: ""
    val state = if (messageError != "") {
        IBFrameState.ERROR(LocalColorScheme.current)
    } else {
        IBFrameState.DEFAULT(LocalColorScheme.current)
    }

    IBankInputFieldBase(
        state = state,
        text = uiState.transactionDescription ?: "",
        placeholderText = "Diễn giải giao dịch",
        required = true,
        maxLengthText = Constants.MAX_LENGTH_TEXT_INPUT_255,
        onClickClear = {
            onValueChange(AddPaymentItem.TransactionDescription, "")
        },
        helpTextLeft = messageError,
    ) { textFieldValue ->
        onValueChange(AddPaymentItem.TransactionDescription, textFieldValue.text)
    }
}