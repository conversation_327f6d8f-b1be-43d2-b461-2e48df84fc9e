package vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.government.service.model.AddPaymentDTO
import java.util.Date

class AddPaymentReducer :
    Reducer<AddPaymentReducer.AddPaymentState, AddPaymentReducer.AddPaymentEvent, AddPaymentReducer.AddPaymentEffect> {

    @Immutable
    data class AddPaymentState(
        val isValid: Boolean = false,
        val declarationNumber: String? = null,
        val date: Date? = null,
        val listChapterCode: List<String>? = null,
        val chapterCodeSelected: String? = null,
        val showChapterCodeBottomSheet: Boolean = false,
        val listEconomicCode: List<String>? = null,
        val economicCodeSelected: String? = null,
        val showEconomicCodeBottomSheet: Boolean = false,
        val listCurrencyType: List<String>? = null,
        val currencyTypeSelected: String? = null,
        val showCurrencyTypeBottomSheet: Boolean = false,
        val listTaxType: List<String>? = null,
        val taxTypeSelected: String? = null,
        val showTaxTypeBottomSheet: Boolean = false,
        val listCustomsCurrency: List<String>? = null,
        val customsCurrencySelected: String? = null,
        val showCustomsCurrencyBottomSheet: Boolean = false,
        val listTradeType: List<String>? = null,
        val tradeTypeSelected: String? = null,
        val showTradeTypeBottomSheet: Boolean = false,
        val amount: String? = null,
        val transactionDescription: String? = null,
        val fieldError: Map<Class<out AddPaymentItem>, String> = mutableMapOf()
    ) : ViewState

    @Immutable
    sealed class AddPaymentEvent : ViewEvent {
        data object PopBack: AddPaymentEvent()

        data object ValidateField : AddPaymentEvent()

        data class ValueTextChange(
            val declarationNumber: String? = null,
            val amount: String? = null,
            val transactionDescription: String? = null
        ) : AddPaymentEvent()

        data class SelectDate(val selectDate: Date?) : AddPaymentEvent()
        data object ClearDate : AddPaymentEvent()

        sealed class ChapterCodeEvent : AddPaymentEvent() {
            data class GetListChapterCodeSuccess(val listChapterCode: List<String>? = null) :
                ChapterCodeEvent()

            data class ChapterCodeSelected(val chapterCodeSelected: String? = null) :
                ChapterCodeEvent()

            data class ShowChapterCodeBottomSheet(val showChapterCodeBottomSheet: Boolean = false) :
                ChapterCodeEvent()

            data object ClearChapterCode : ChapterCodeEvent()
        }

        sealed class EconomicCodeEvent : AddPaymentEvent() {
            data class GetListEconomicCodeSuccess(val listEconomicCode: List<String>? = null) :
                EconomicCodeEvent()

            data class EconomicCodeSelected(val economicCodeSelected: String? = null) :
                EconomicCodeEvent()

            data class ShowEconomicCodeBottomSheet(val showEconomicCodeBottomSheet: Boolean = false) :
                EconomicCodeEvent()

            data object ClearEconomicCode : EconomicCodeEvent()
        }

        sealed class CurrencyTypeEvent : AddPaymentEvent() {
            data class GetListCurrencyTypeSuccess(val listCurrencyType: List<String>? = null) :
                CurrencyTypeEvent()

            data class CurrencyTypeSelected(val currencyTypeSelected: String? = null) :
                CurrencyTypeEvent()

            data class ShowCurrencyTypeBottomSheet(val showCurrencyTypeBottomSheet: Boolean = false) :
                CurrencyTypeEvent()

            data object ClearCurrencyType : CurrencyTypeEvent()
        }

        sealed class TaxTypeEvent : AddPaymentEvent() {
            data class GetListTaxTypeSuccess(val listTaxType: List<String>? = null) :
                TaxTypeEvent()

            data class TaxTypeSelected(val taxTypeSelected: String? = null) :
                TaxTypeEvent()

            data class ShowTaxTypeBottomSheet(val showTaxTypeBottomSheet: Boolean = false) :
                TaxTypeEvent()

            data object ClearTaxType : TaxTypeEvent()
        }

        sealed class CustomsCurrencyEvent : AddPaymentEvent() {
            data class GetListCustomsCurrencySuccess(val listCustomsCurrency: List<String>? = null) :
                CustomsCurrencyEvent()

            data class CustomsCurrencySelected(val customsCurrencySelected: String? = null) :
                CustomsCurrencyEvent()

            data class ShowCustomsCurrencyBottomSheet(val showCustomsCurrencyBottomSheet: Boolean = false) :
                CustomsCurrencyEvent()

            data object ClearCustomsCurrency : CustomsCurrencyEvent()
        }

        sealed class TradeTypeEvent : AddPaymentEvent() {
            data class GetListTradeTypeSuccess(val listTradeType: List<String>? = null) :
                TradeTypeEvent()

            data class TradeTypeSelected(val tradeTypeSelected: String? = null) :
                TradeTypeEvent()

            data class ShowTradeTypeBottomSheet(val showTradeTypeBottomSheet: Boolean = false) :
                TradeTypeEvent()

            data object ClearTradeType : TradeTypeEvent()
        }

    }

    @Immutable
    sealed class AddPaymentEffect : SideEffect {
        data object GetListChapterCode : AddPaymentEffect()
        data object GetListEconomicCode : AddPaymentEffect()
        data object GetListCurrencyType : AddPaymentEffect()
        data object GetListTaxType : AddPaymentEffect()
        data object GetListCustomsCurrency : AddPaymentEffect()
        data object GetListTradeType : AddPaymentEffect()
        data class SendDataToMain(val addPaymentDTO: AddPaymentDTO? = null) : AddPaymentEffect()
    }

    override fun reduce(
        previousState: AddPaymentState, event: AddPaymentEvent
    ): Pair<AddPaymentState, AddPaymentEffect?> {

        return when (event) {
            is AddPaymentEvent.PopBack -> {
                previousState.copy(isValid = true) to null
            }

            is AddPaymentEvent.ValidateField -> {
                handleValidateField(previousState)
            }

            is AddPaymentEvent.ValueTextChange -> {
                previousState.copy(
                    declarationNumber = event.declarationNumber ?: previousState.declarationNumber,
                    amount = event.amount ?: previousState.amount,
                    transactionDescription = event.transactionDescription
                        ?: previousState.transactionDescription
                ) to null
            }

            is AddPaymentEvent.SelectDate -> {
                previousState.copy(date = event.selectDate ?: previousState.date) to null
            }

            is AddPaymentEvent.ClearDate -> {
                previousState.copy(date = null) to null
            }

            is AddPaymentEvent.ChapterCodeEvent -> reduceChapterCodeEvent(previousState, event)
            is AddPaymentEvent.EconomicCodeEvent -> reduceEconomicCodeEvent(previousState, event)
            is AddPaymentEvent.CurrencyTypeEvent -> reduceCurrencyTypeEvent(previousState, event)
            is AddPaymentEvent.TaxTypeEvent -> reduceTaxTypeEvent(previousState, event)
            is AddPaymentEvent.CustomsCurrencyEvent -> reduceCustomsCurrencyEvent(
                previousState,
                event
            )

            is AddPaymentEvent.TradeTypeEvent -> reduceTradeTypeEvent(previousState, event)

        }
    }

    private fun reduceChapterCodeEvent(
        previousState: AddPaymentState,
        event: AddPaymentEvent.ChapterCodeEvent
    ): Pair<AddPaymentState, AddPaymentEffect?> {
        return when (event) {
            is AddPaymentEvent.ChapterCodeEvent.GetListChapterCodeSuccess -> {
                previousState.copy(listChapterCode = event.listChapterCode) to null
            }

            is AddPaymentEvent.ChapterCodeEvent.ChapterCodeSelected -> {
                previousState.copy(
                    chapterCodeSelected = event.chapterCodeSelected
                        ?: previousState.chapterCodeSelected
                ) to null
            }

            is AddPaymentEvent.ChapterCodeEvent.ShowChapterCodeBottomSheet -> {
                val effect =
                    if (event.showChapterCodeBottomSheet) AddPaymentEffect.GetListChapterCode else null
                previousState.copy(
                    showChapterCodeBottomSheet = event.showChapterCodeBottomSheet
                ) to effect

            }

            is AddPaymentEvent.ChapterCodeEvent.ClearChapterCode -> {
                previousState.copy(chapterCodeSelected = null) to null
            }
        }
    }

    private fun reduceEconomicCodeEvent(
        previousState: AddPaymentState,
        event: AddPaymentEvent.EconomicCodeEvent
    ): Pair<AddPaymentState, AddPaymentEffect?> {
        return when (event) {
            is AddPaymentEvent.EconomicCodeEvent.GetListEconomicCodeSuccess -> {
                previousState.copy(listEconomicCode = event.listEconomicCode) to null
            }

            is AddPaymentEvent.EconomicCodeEvent.EconomicCodeSelected -> {
                previousState.copy(
                    economicCodeSelected = event.economicCodeSelected
                        ?: previousState.economicCodeSelected
                ) to null
            }

            is AddPaymentEvent.EconomicCodeEvent.ShowEconomicCodeBottomSheet -> {
                val effect =
                    if (event.showEconomicCodeBottomSheet) AddPaymentEffect.GetListEconomicCode else null
                previousState.copy(
                    showEconomicCodeBottomSheet = event.showEconomicCodeBottomSheet
                ) to effect

            }

            is AddPaymentEvent.EconomicCodeEvent.ClearEconomicCode -> {
                previousState.copy(economicCodeSelected = null) to null
            }
        }
    }

    private fun reduceCurrencyTypeEvent(
        previousState: AddPaymentState,
        event: AddPaymentEvent.CurrencyTypeEvent
    ): Pair<AddPaymentState, AddPaymentEffect?> {
        return when (event) {
            is AddPaymentEvent.CurrencyTypeEvent.GetListCurrencyTypeSuccess -> {
                previousState.copy(listCurrencyType = event.listCurrencyType) to null
            }

            is AddPaymentEvent.CurrencyTypeEvent.CurrencyTypeSelected -> {
                previousState.copy(
                    currencyTypeSelected = event.currencyTypeSelected
                        ?: previousState.currencyTypeSelected
                ) to null
            }

            is AddPaymentEvent.CurrencyTypeEvent.ShowCurrencyTypeBottomSheet -> {
                val effect =
                    if (event.showCurrencyTypeBottomSheet) AddPaymentEffect.GetListCurrencyType else null
                previousState.copy(
                    showCurrencyTypeBottomSheet = event.showCurrencyTypeBottomSheet
                ) to effect

            }

            is AddPaymentEvent.CurrencyTypeEvent.ClearCurrencyType -> {
                previousState.copy(currencyTypeSelected = null) to null
            }
        }
    }

    private fun reduceTaxTypeEvent(
        previousState: AddPaymentState,
        event: AddPaymentEvent.TaxTypeEvent
    ): Pair<AddPaymentState, AddPaymentEffect?> {
        return when (event) {
            is AddPaymentEvent.TaxTypeEvent.GetListTaxTypeSuccess -> {
                previousState.copy(listTaxType = event.listTaxType) to null
            }

            is AddPaymentEvent.TaxTypeEvent.TaxTypeSelected -> {
                previousState.copy(
                    taxTypeSelected = event.taxTypeSelected
                        ?: previousState.taxTypeSelected
                ) to null
            }

            is AddPaymentEvent.TaxTypeEvent.ShowTaxTypeBottomSheet -> {
                val effect =
                    if (event.showTaxTypeBottomSheet) AddPaymentEffect.GetListTaxType else null
                previousState.copy(
                    showTaxTypeBottomSheet = event.showTaxTypeBottomSheet
                ) to effect

            }

            is AddPaymentEvent.TaxTypeEvent.ClearTaxType -> {
                previousState.copy(taxTypeSelected = null) to null
            }
        }
    }

    private fun reduceCustomsCurrencyEvent(
        previousState: AddPaymentState,
        event: AddPaymentEvent.CustomsCurrencyEvent
    ): Pair<AddPaymentState, AddPaymentEffect?> {
        return when (event) {
            is AddPaymentEvent.CustomsCurrencyEvent.GetListCustomsCurrencySuccess -> {
                previousState.copy(listCustomsCurrency = event.listCustomsCurrency) to null
            }

            is AddPaymentEvent.CustomsCurrencyEvent.CustomsCurrencySelected -> {
                previousState.copy(
                    customsCurrencySelected = event.customsCurrencySelected
                        ?: previousState.customsCurrencySelected
                ) to null
            }

            is AddPaymentEvent.CustomsCurrencyEvent.ShowCustomsCurrencyBottomSheet -> {
                val effect =
                    if (event.showCustomsCurrencyBottomSheet) AddPaymentEffect.GetListCustomsCurrency else null
                previousState.copy(
                    showCustomsCurrencyBottomSheet = event.showCustomsCurrencyBottomSheet
                ) to effect

            }

            is AddPaymentEvent.CustomsCurrencyEvent.ClearCustomsCurrency -> {
                previousState.copy(customsCurrencySelected = null) to null
            }
        }
    }

    private fun reduceTradeTypeEvent(
        previousState: AddPaymentState,
        event: AddPaymentEvent.TradeTypeEvent
    ): Pair<AddPaymentState, AddPaymentEffect?> {
        return when (event) {
            is AddPaymentEvent.TradeTypeEvent.GetListTradeTypeSuccess -> {
                previousState.copy(listTradeType = event.listTradeType) to null
            }

            is AddPaymentEvent.TradeTypeEvent.TradeTypeSelected -> {
                previousState.copy(
                    tradeTypeSelected = event.tradeTypeSelected
                        ?: previousState.tradeTypeSelected
                ) to null
            }

            is AddPaymentEvent.TradeTypeEvent.ShowTradeTypeBottomSheet -> {
                val effect =
                    if (event.showTradeTypeBottomSheet) AddPaymentEffect.GetListTradeType else null
                previousState.copy(
                    showTradeTypeBottomSheet = event.showTradeTypeBottomSheet
                ) to effect

            }

            is AddPaymentEvent.TradeTypeEvent.ClearTradeType -> {
                previousState.copy(tradeTypeSelected = null) to null
            }
        }
    }

    private fun handleValidateField(previousState: AddPaymentState): Pair<AddPaymentState, AddPaymentEffect?> {
        val errors = mutableMapOf<Class<out AddPaymentItem>, String>()
        errors[AddPaymentItem.DeclarationNumber::class.java] =
            if (previousState.declarationNumber.isNullOrBlank()) {
                "Số tờ khai/QĐ/TB không được để trống"
            } else ""

        errors[AddPaymentItem.InputMoney::class.java] = if (previousState.amount.isNullOrBlank()) {
            "Số tiền không được để trống"
        } else ""

        errors[AddPaymentItem.TransactionDescription::class.java] =
            if (previousState.transactionDescription.isNullOrBlank()) {
                "Diễn giải giao dịch không được để trống"
            } else ""

        errors[AddPaymentItem.Date::class.java] = if (previousState.date == null) {
            "Ngày tờ khai QĐ/TB không được để trống"
        } else ""

        errors[AddPaymentItem.DropDownItem.ChapterCode::class.java] =
            if (previousState.chapterCodeSelected == null) {
                "Mã chương không được để trống"
            } else ""

        errors[AddPaymentItem.DropDownItem.EconomicCode::class.java] =
            if (previousState.economicCodeSelected == null) {
                "Mã nội dung kinh tế không được để trống"
            } else ""

        errors[AddPaymentItem.DropDownItem.CurrencyType::class.java] =
            if (previousState.economicCodeSelected == null) {
                "Loại tiền không được để trống"
            } else ""

        errors[AddPaymentItem.DropDownItem.TaxType::class.java] =
            if (previousState.economicCodeSelected == null) {
                "Sắc thuế không được để trống"
            } else ""

        errors[AddPaymentItem.DropDownItem.CustomsCurrency::class.java] =
            if (previousState.customsCurrencySelected == null) {
                "Loại tiền HQ không được để trống"
            } else ""

        errors[AddPaymentItem.DropDownItem.TradeType::class.java] =
            if (previousState.tradeTypeSelected == null) {
                "Loại hình XNK không được để trống"
            } else ""

        val isValid = errors.values.all { it.isEmpty() }
        val effect = if (isValid) {
            val addPaymentDTO = AddPaymentDTO(
                declarationNo = previousState.declarationNumber ?: "--",
                date = previousState.date ?: Date(),
                chapterCodeSelected = previousState.chapterCodeSelected ?: "--",
                economicCodeSelected = previousState.economicCodeSelected ?: "--",
                currencyTypeSelected = previousState.currencyTypeSelected ?: "--",
                taxTypeSelected = previousState.taxTypeSelected ?: "--",
                customsCurrencySelected = previousState.customsCurrencySelected ?: "--",
                tradeTypeSelected = previousState.tradeTypeSelected ?: "--",
                amount = previousState.amount ?: "--",
                transactionDescription = previousState.transactionDescription ?: "--",
                ecName = "undefined",
                eiTypeName = "undefined",
            )
            AddPaymentEffect.SendDataToMain(addPaymentDTO)
        } else null
        return previousState.copy(fieldError = errors) to effect
    }
}