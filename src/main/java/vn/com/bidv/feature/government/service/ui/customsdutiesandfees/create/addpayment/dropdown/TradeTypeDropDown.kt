package vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankInputDropdownBaseV2
import vn.com.bidv.designsystem.component.dataentry.IBankInputDropdownTypeData
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankSearchDialog
import vn.com.bidv.designsystem.component.feedback.bottomsheet.SearchDialogState
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.AddPaymentItem
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.AddPaymentReducer
import vn.com.bidv.localization.R

@Composable
fun TradeTypeDropDown(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
) {
    val messageError =
        uiState.fieldError?.get(AddPaymentItem.DropDownItem.TradeType::class.java) ?: ""
    val state = if (messageError != "") {
        IBFrameState.ERROR(LocalColorScheme.current)
    } else {
        IBFrameState.DEFAULT(LocalColorScheme.current)
    }

    IBankInputDropdownBaseV2(
        labelText = "Loại hình XNK",
        required = true,
        typeData = IBankInputDropdownTypeData.Select(text = uiState.tradeTypeSelected ?: ""),
        iconEnd = vn.com.bidv.designsystem.R.drawable.arrow_bottom_outline,
        hintTextStart = messageError,
        state = state,
        onClickEnd = {
            onEvent(
                AddPaymentReducer.AddPaymentEvent.TradeTypeEvent.ShowTradeTypeBottomSheet(
                    true
                )
            )
        },
        onClickClear = { onEvent(AddPaymentReducer.AddPaymentEvent.TradeTypeEvent.ClearTradeType) }
    )
}

@Composable
fun ShowTradeTypeBottomSheet(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit
) {
    if (uiState.showTradeTypeBottomSheet) {
        IBankSearchDialog(
            title = "Loại hình XNK",
            itemSelected = uiState.tradeTypeSelected,
            compareKey = {},
            showSearchBox = false,
            listData = uiState.listTradeType,
            searchFilter = { _, _ -> true },
            state = SearchDialogState.CONTENT,
            onRequestDismiss = {
                onEvent(
                    AddPaymentReducer.AddPaymentEvent.TradeTypeEvent.ShowTradeTypeBottomSheet(
                        false
                    )
                )
                onEvent(AddPaymentReducer.AddPaymentEvent.TradeTypeEvent.TradeTypeSelected(it))
            },
            listSearchFilterText = listOf(),
            errorView = {
                IBankEmptyState(
                    modifier = Modifier.fillMaxSize(),
                    supportingText = stringResource(R.string.khong_tai_duoc_du_lieu),
                    textButton = stringResource(R.string.thu_lai)
                )
            }
        ) { index, searchItem ->
            TradeTypeDropDownItem(
                (uiState.listTradeType?.get(index) ?: "") == uiState.tradeTypeSelected,
                searchItem.data
            )
        }
    }
}

@Composable
fun TradeTypeDropDownItem(
    isSelected: Boolean = false, value: String
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    Row(
        Modifier
            .fillMaxWidth()
            .background(
                color = if (isSelected) colorScheme.bgBrand_01Tertiary else Color.Transparent,
                shape = RoundedCornerShape(IBSpacing.spacingXs)
            )
            .padding(IBSpacing.spacingM), verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f), verticalArrangement = Arrangement.Center
        ) {
            Text(
                color = colorScheme.contentMainPrimary,
                text = value,
                style = typography.bodyBody_l,
                modifier = Modifier.wrapContentWidth()
            )
        }
        Box(modifier = Modifier.size(32.dp)) {
            if (isSelected) {
                Icon(
                    modifier = Modifier.size(20.dp),
                    painter = painterResource(id = vn.com.bidv.designsystem.R.drawable.check),
                    contentDescription = ""
                )
            }
        }

    }
}


