package vn.com.bidv.feature.government.service.data

import vn.com.bidv.feature.common.data.masterdata.apis.MasterDataApi
import vn.com.bidv.feature.common.data.masterdata.model.CustomerDto
import vn.com.bidv.feature.common.data.masterdata.model.InqCustomerCriteriaDto
import vn.com.bidv.feature.government.service.data.governmentservice.apis.GovernmentServiceApi
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListInquiryCustomsDutyRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListTxnPendingListRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.InquiryCustomsDutyReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnPendingListReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnDetailReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnDetailRes
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.network.domain.BaseRepository
import javax.inject.Inject

class GovernmentServiceRepository @Inject constructor(
    private val service: GovernmentServiceApi,
    private val customerInfoApi: MasterDataApi,
) : BaseRepository() {

    suspend fun getListPendingTransaction(
        request: TxnPendingListReq
    ): NetworkResult<DataListTxnPendingListRes> = launch {
        service.listPending(request)
    }

    suspend fun getPendingTransactionDetail(txnDetailReq: TxnDetailReq): NetworkResult<TxnDetailRes> =
          launch { service.detail(txnDetailReq) }

    suspend fun inquiryTransaction(request: InquiryCustomsDutyReq): NetworkResult<DataListInquiryCustomsDutyRes> =
          launch { service.inquiry(request) }

    suspend fun getTaxPayerInformation(request: InqCustomerCriteriaDto): NetworkResult<CustomerDto> =
        launch { customerInfoApi.getCustomerInfo(request) }
}
