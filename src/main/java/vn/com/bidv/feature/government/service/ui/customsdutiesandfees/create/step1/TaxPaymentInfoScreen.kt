package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step1

import android.widget.Toast
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.flow.Flow
import timber.log.Timber
import vn.com.bidv.common.utils.CollectSideEffect
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.IBankSectionHeader
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.LeadingType
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankInputFieldBase
import vn.com.bidv.designsystem.component.dataentry.datacard.IBankDataCard
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.navigation.button.IBankLinkButton
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonSize
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBankTheme
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.feature.government.service.common.Constants
import vn.com.bidv.feature.government.service.domain.model.InquiryCustomsDutyDMO
import vn.com.bidv.feature.government.service.model.AddPaymentDTO
import vn.com.bidv.feature.government.service.model.IViewTransaction
import vn.com.bidv.feature.government.service.model.SubmissionType
import vn.com.bidv.feature.government.service.model.TaxPayerInfoField
import vn.com.bidv.feature.government.service.navigation.GovernmentServiceRoute
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.inquirypayment.InquiryTransactionScreenConstants
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.step1.TaxPaymentRadioPicker
import vn.com.bidv.feature.government.service.ui.common.TaxPaymentEntryCard
import vn.com.bidv.feature.government.service.ui.common.TaxPaymentEntryCardActionScenario
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.data.ShareDataDTO
import vn.com.bidv.sdkbase.utils.formatMoney
import java.lang.reflect.Type

@Composable
fun TaxPaymentInfoScreen(
    navController: NavHostController,
    progressBar: @Composable () -> Unit,
) {
    val context = LocalContext.current
    val viewModel: TaxPaymentInfoViewModel = hiltViewModel()

    BaseScreen(
        navController = navController,
        viewModel = viewModel,
        topAppBarType = TopAppBarType.Title,
        topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(R.string.thong_tin_khoan_nop),
            showHomeIcon = true,
            actionItems = {
                IBankLinkButton(
                    modifier = Modifier,
                    text = stringResource(R.string.mau_giao_dich),
                    leadingIcon = ImageVector.vectorResource(vn.com.bidv.designsystem.R.drawable.quan_ly_mau_giao_dich_outline)
                ) {
                    Toast.makeText(context, context.getString(R.string.mau_giao_dich), Toast.LENGTH_SHORT).show()
                }
            }
        )
    ) { viewState, viewEvent ->

        if (viewState.isInitialized.not()) {
            viewEvent(TaxPaymentInfoReducer.TaxPaymentViewEvent.InitScreen)
        }

        val totalAmount = remember(viewState) {
            val target = if (viewState.modeDelegate) {
                viewState.nopThayManualEntries
            } else {
                viewState.nopDNManualEntries + viewState.fetchedTaxId
            }

            target
                .filter { it.isChecked }
                .sumOf { it.data.getValueAmount().toLong() }
                .toString()
        }
        Column {
            progressBar()
            Box {
                Column(Modifier.fillMaxHeight().verticalScroll(rememberScrollState())) {
                    TaxPaymentRadioPicker(
                        Modifier.padding(start = IBSpacing.spacingM, end = IBSpacing.spacingM, top = IBSpacing.spacingM)
                    ) {
                        viewEvent(
                            TaxPaymentInfoReducer.TaxPaymentViewEvent.ChangeModeDelegate(
                                modeDelegate = it == SubmissionType.ON_BEHALF
                            )
                        )
                    }
                    TaxPaymentInfoContent(navController, viewState, viewEvent)
                    Spacer(Modifier.height(120.dp))
                }
                IBankActionBar(
                    modifier = Modifier.align(Alignment.BottomCenter),
                    buttonPositive = DialogButtonInfo(stringResource(R.string.tiep_tuc)),
                    isVertical = false,
                    leadingIcon = ImageVector.vectorResource(vn.com.bidv.designsystem.R.drawable.khoan_phai_thu),
                    title = stringResource(R.string.tong_tien),
                    description = totalAmount.formatMoney("VND", true),
                )
            }
        }
    }

    CollectSideEffect<List<InquiryCustomsDutyDMO>>(
        viewModel.subscribeShareData(InquiryTransactionScreenConstants.SHAREDATA_KEY),
        typeToken = object: TypeToken<List<InquiryCustomsDutyDMO>>() {}.type,
        onResult = {
            Timber.d(it.toString())
            viewModel.sendEvent(TaxPaymentInfoReducer.TaxPaymentViewEvent.AddInquiryItems(it))
        },
        onError = {
        }
    )

    CollectSideEffect<AddPaymentDTO>(
        viewModel.subscribeShareData(Constants.ADD_PAYMENT_DATA),
        typeToken = object: TypeToken<AddPaymentDTO>() {}.type,
        onResult = {
            Timber.d(it.toString())
            viewModel.sendEvent(TaxPaymentInfoReducer.TaxPaymentViewEvent.AddManualItems(listOf(it)))
        },
        onError = {
        }
    )
}

@Composable
fun TaxPaymentInfoContent(
    navController: NavHostController,
    viewState: TaxPaymentInfoReducer.TaxPaymentViewState,
    handleEvent: (TaxPaymentInfoReducer.TaxPaymentViewEvent) -> Unit,
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current

    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp),
        modifier = Modifier
            .padding(12.dp)
            .fillMaxSize()
    ) {
        if (viewState.modeDelegate) {
            DelegatorTaxPayerInfoSection(viewState) { field, value ->
                handleEvent(
                    TaxPaymentInfoReducer.TaxPaymentViewEvent.UpdateTaxDelegatorInfo(
                        field,
                        value
                    )
                )
            }
        }

        TaxPayerInfoSection(viewState) { field, value ->
            handleEvent(TaxPaymentInfoReducer.TaxPaymentViewEvent.UpdateTaxPayerInfo(field, value))
        }

        IBankDataCard(cardHeader = {
            IBankSectionHeader(
                shLeadingType = LeadingType.Dash(),
                shSectionTitle = stringResource(R.string.danh_sach_thue_can_nop),
                thumbContent = {
                    IBankNormalButton(
                        text = stringResource(R.string.van_tin),
                        size = NormalButtonSize.SM(typography),
                        type = NormalButtonType.SECONDARYGRAY(colorScheme)
                    ) {
                        navController.navigate(
                            GovernmentServiceRoute.InquiryTransactionScreen("**********")
                        )
                    }
                }
            )
        }, isChecked = false, cardFooter = {}, onCheckedChange = {}, cardContent = {
            Column {
                val taxEntriesCalculation = remember(viewState) {
                    if (viewState.modeDelegate) {
                        viewState.nopThayManualEntries
                    } else {
                        (viewState.nopDNManualEntries + viewState.fetchedTaxId)
                    }
                }

                ListTaxPayment(
                    navController,
                    taxEntries = taxEntriesCalculation
                ) {
                    handleEvent(TaxPaymentInfoReducer.TaxPaymentViewEvent.SelectTaxEntry(it))
                }
                IBankNormalButton(
                    text = stringResource(R.string.them_khoan_nop),
                    type = NormalButtonType.SECONDARYGRAY(colorScheme),
                    modifier = Modifier
                        .padding(top = 12.dp)
                        .padding(horizontal = 12.dp)
                        .fillMaxWidth()
                ) {
                    navController.navigate(GovernmentServiceRoute.AddPaymentRouter.route)
                }
            }
        })
    }
}

@Composable
private fun TaxPayerInfoSection(
    viewState: TaxPaymentInfoReducer.TaxPaymentViewState,
    onTextChange: (field: TaxPayerInfoField, value: String) -> Unit,
) {
    val colorScheme = LocalColorScheme.current
    IBankDataCard(cardHeader = {
        IBankSectionHeader(
            shLeadingType = LeadingType.Dash(),
            shSectionTitle = if (viewState.modeDelegate)
                stringResource(R.string.thong_tin_nguoi_nop_thay)
            else stringResource(R.string.thong_tin_nguoi_nop_thue),
        )
    }, isChecked = false, cardFooter = {}, onCheckedChange = {}, cardContent = {
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier.padding(horizontal = 12.dp)
        ) {
            IBankInputFieldBase(
                required = true,
                placeholderText = stringResource(R.string.ma_so_thue_nguoi_nop_thue),
                state = IBFrameState.DISABLE(colorScheme),
                text = viewState.taxPayerInfo.taxId,
            ) {
                onTextChange(TaxPayerInfoField.TAX_ID, it.text)
            }
            IBankInputFieldBase(
                required = true,
                placeholderText = stringResource(R.string.ho_ten_nguoi_nop_thue),
                text = viewState.taxPayerInfo.name,
            ) {
                onTextChange(TaxPayerInfoField.NAME, it.text)
            }
            IBankInputFieldBase(
                required = true,
                placeholderText = stringResource(R.string.dia_chi_nguoi_nop_thue),
                text = viewState.taxPayerInfo.address,
            ) {
                onTextChange(TaxPayerInfoField.ADDRESS, it.text)
            }
        }
    })
}

@Composable
private fun DelegatorTaxPayerInfoSection(
    viewState: TaxPaymentInfoReducer.TaxPaymentViewState,
    onTextChange: (field: TaxPayerInfoField, value: String) -> Unit,
) {
    IBankDataCard(
        showCheckbox = false, isChecked = false, cardHeader = {
            IBankSectionHeader(
                shLeadingType = LeadingType.Dash(),
                shSectionTitle = stringResource(R.string.thong_tin_nguoi_nop_thay),
            )
        }, cardFooter = {}, onCheckedChange = {},
        cardContent = {
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp),
                modifier = Modifier.padding(horizontal = 12.dp)
            ) {
                IBankInputFieldBase(
                    required = true,
                    placeholderText = stringResource(R.string.ma_so_nguoi_nop_thay),
                    text = viewState.taxDelegatorInfo.taxId,
                ) {
                    onTextChange(TaxPayerInfoField.TAX_ID, it.text)
                }
                IBankInputFieldBase(
                    required = true,
                    placeholderText = stringResource(R.string.ho_ten_nguoi_nop_thay),
                    text = viewState.taxDelegatorInfo.name,
                ) {
                    onTextChange(TaxPayerInfoField.NAME, it.text)
                }
                IBankInputFieldBase(
                    required = true,
                    placeholderText = stringResource(R.string.dia_chi_nguoi_nop_thay),
                    text = viewState.taxDelegatorInfo.address,
                ) {
                    onTextChange(TaxPayerInfoField.ADDRESS, it.text)
                }
            }
        }
    )
}

@Composable
fun <T: IViewTransaction> ListTaxPayment(
    navController: NavHostController,
    taxEntries: List<ModelCheckAble<T>>,
    onItemCheckedChange: (ModelCheckAble<T>) -> Unit,
) {
    Column(
        modifier = Modifier.padding(horizontal = 8.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        taxEntries.forEach {
            TaxPaymentEntryCard(
                transactionsDMO = it,
                navController = navController,
                actionScenario = TaxPaymentEntryCardActionScenario.NONE,
            ) {
                onItemCheckedChange(it)
            }
        }
    }
}

@Composable
inline fun <reified T> CollectSideEffect(
    flow: Flow<ShareDataDTO>,
    typeToken: Type,
    crossinline onResult: (T) -> Unit,
    crossinline onError: (T?) -> Unit = {}
) {
    CollectSideEffect(
        sideEffect = flow,
        onSideEffect = { sideEffect ->
            val result = Gson().fromJson<T>(sideEffect.data, typeToken)
            if (result != null) {
                onResult(result)
            } else {
                onError(sideEffect as T)
            }
        }
    )
}

@Preview
@Composable
fun PreviewTaxPaymentInfoContent() {
    IBankTheme {
        TaxPaymentInfoContent(
            navController = rememberNavController(),
            viewState = TaxPaymentInfoReducer.TaxPaymentViewState(
                fetchedTaxId = Constants.fakeTransactions.map { ModelCheckAble(it) },
                modeDelegate = true,
            ),
            handleEvent = {}
        )
    }

}