package vn.com.bidv.feature.government.service.ui.list.tabcontent.pendingtransaction.advsearch

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.utils.ModalConfirmConfig
import vn.com.bidv.sdkbase.utils.TransactionStatusBase
import java.util.Date

class AdvSearchReducer :
    Reducer<AdvSearchReducer.AdvSearchState, AdvSearchReducer.AdvSearchEvent, AdvSearchReducer.AdvSearchEffect> {
    @Immutable
    data class AdvSearchState(
        val invalidAmount: Boolean = false,
        val listCurrency: List<String>? = null,
        val listCurrencySelected: List<String>? = null,
        val listStatus: List<TransactionStatusBase>? = null,
        val listStatusSelected: List<TransactionStatusBase>? = null,
        val maxAmount: String? = null,
        val minAmount: String? = null,
        val debit: String? = null,
        val tax: String? = null,
        val declaration: String? = null,
        val batch: String? = null,
        val startDate: Date? = null,
        val endDate: Date? = null,
        val modalConfirmConfig: ModalConfirmConfig? = null,
    ) : ViewState

    @Immutable
    sealed class AdvSearchEvent : ViewEvent {
        data class ValueTextChanged(
            val maxAmount: String? = null,
            val minAmount: String? = null,
            val creator: String? = null,
            val debit: String? = null,
            val tax: String? = null,
            val declaration: String? = null,
            val batch: String? = null,
        ) : AdvSearchEvent()

        data object ClearSearchFilterContent : AdvSearchEvent()

        data object ShowCurrencyBottomSheet : AdvSearchEvent()
        data object ShowStatusBottomSheet : AdvSearchEvent()

        data class GetCurrencyListSuccess(val listCurrency: List<String>?) :
            AdvSearchEvent()

        data class GetStatusListSuccess(val listStatus: List<TransactionStatusBase>?) :
            AdvSearchEvent()

        data class SetCurrencyList(val listCurrencySelected: List<String>?) : AdvSearchEvent()
        data class SetStatusList(val listStatusSelected: List<TransactionStatusBase>?) : AdvSearchEvent()

        data class ValidateAmount(val invalidAmount: Boolean) : AdvSearchEvent()

        data object ClearDate : AdvSearchEvent()
        data class SelectDate(val startDate: Date?, val endDate: Date?) : AdvSearchEvent()
        data class ShowError(val modalConfirm: ModalConfirmConfig?) : AdvSearchEvent()
    }

    @Immutable
    sealed class AdvSearchEffect : SideEffect {
        data object FetCurrencyList : AdvSearchEffect()
        data object FetStatusList : AdvSearchEffect()
    }

    override fun reduce(
        previousState: AdvSearchState,
        event: AdvSearchEvent
    ): Pair<AdvSearchState, AdvSearchEffect?> {
        val currentState = previousState as? AdvSearchState ?: return previousState to null
        return when (event) {
            is AdvSearchEvent.SelectDate -> {
                currentState.copy(
                    startDate = event.startDate,
                    endDate = event.endDate,
                ) to null
            }

            is AdvSearchEvent.ClearSearchFilterContent -> {
                AdvSearchState() to null
            }

            is AdvSearchEvent.ShowCurrencyBottomSheet -> {
                currentState to AdvSearchEffect.FetCurrencyList
            }

            is AdvSearchEvent.ShowStatusBottomSheet -> {
                currentState to AdvSearchEffect.FetStatusList
            }

            is AdvSearchEvent.ValidateAmount -> {
                if (currentState.invalidAmount != event.invalidAmount)
                    currentState.copy(invalidAmount = event.invalidAmount) to null
                else currentState to null
            }

            is AdvSearchEvent.ValueTextChanged -> {
                currentState.copy(
                    minAmount = event.minAmount ?: currentState.minAmount,
                    maxAmount = event.maxAmount ?: currentState.maxAmount,
                    debit = event.debit ?: currentState.debit,
                    tax = event.tax ?: currentState.tax,
                    declaration = event.declaration ?: currentState.declaration,
                    batch = event.batch ?: currentState.batch,
                ) to null
            }

            is AdvSearchEvent.GetCurrencyListSuccess -> {
                currentState.copy(
                    listCurrency = event.listCurrency,
                    listCurrencySelected = if (currentState.listCurrencySelected.isNullOrEmpty()) event.listCurrency else currentState.listCurrencySelected
                ) to null
            }

            is AdvSearchEvent.GetStatusListSuccess -> {
                currentState.copy(
                    listStatus = event.listStatus,
                    listStatusSelected = if (currentState.listStatusSelected.isNullOrEmpty()) event.listStatus else currentState.listStatusSelected
                ) to null
            }

            is AdvSearchEvent.SetCurrencyList -> {
                currentState.copy(listCurrencySelected = event.listCurrencySelected) to null
            }

            is AdvSearchEvent.SetStatusList -> {
                currentState.copy(listStatusSelected = event.listStatusSelected) to null
            }

            is AdvSearchEvent.ClearDate -> {
                currentState.copy(startDate = null, endDate = null) to null
            }

            is AdvSearchEvent.ShowError -> {
                currentState.copy(modalConfirmConfig = event.modalConfirm) to null
            }
        }
    }
}
