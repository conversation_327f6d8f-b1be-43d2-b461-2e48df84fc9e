package vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.step1

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.component.dataentry.IBankRadioButtonWithText
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

@Composable
fun CustomRadioOption(
    modifier: Modifier = Modifier,
    text: String,
    selected: Boolean,
    iconResId: Int,
    onClick: () -> Unit
) {
    val color = LocalColorScheme.current
    val typography = LocalTypography.current

    val borderColor = if (selected) color.borderBrandPrimary else color.borderMainPrimary
    val backgroundColor = color.bgMainTertiary
    val textColor = if (selected) color.contentMainPrimary else color.contentMainTertiary
    val shadowColor = color.effectsFocus_ringBrand

    val cornerRadius = IBSpacing.spacingXs
    val focusRingStrokeWidth = IBSpacing.spacingXs
    val shadowPadding = 2.dp

    Box(
        modifier = modifier
            .padding(shadowPadding)
            .drawBehind {
                if (selected) {
                    drawRoundRect(
                        color = shadowColor,
                        cornerRadius = CornerRadius(cornerRadius.toPx()),
                        style = Stroke(width = focusRingStrokeWidth.toPx())
                    )
                }
            }
            .selectable(
                selected = selected,
                onClick = onClick,
                role = Role.RadioButton
            )
    ) {
        Card(
            shape = RoundedCornerShape(cornerRadius),
            border = BorderStroke(1.dp, borderColor),
            colors = CardDefaults.cardColors(
                containerColor = backgroundColor
            ),
            elevation = CardDefaults.cardElevation(0.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(IBSpacing.spacingM),
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Image(
                        imageVector = ImageVector.vectorResource(iconResId),
                        contentDescription = null,
                        modifier = Modifier.size(IBSpacing.spacing2xl)
                    )

                    Spacer(modifier = Modifier.height(IBSpacing.spacingXs))

                    Text(
                        text = text,
                        style = typography.bodyBody_l,
                        color = textColor
                    )
                }

                IBankRadioButtonWithText(checked = selected) {}
            }
        }
    }
}



@Preview(showBackground = true)
@Composable
fun showCustomRadioOption() {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
            .selectableGroup(),
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {

        CustomRadioOption(
            text = "Nộp cho DN",
            selected = true,
            iconResId = vn.com.bidv.designsystem.R.drawable.nop_thue_tron_outline,
            modifier = Modifier.weight(1f),
            onClick = {

            }
        )


        CustomRadioOption(
            text = "Nộp thay",
            selected = false,
            iconResId = vn.com.bidv.designsystem.R.drawable.nop_thay_thue_outline,
            modifier = Modifier.weight(1f),
            onClick = {

            }
        )
    }
}