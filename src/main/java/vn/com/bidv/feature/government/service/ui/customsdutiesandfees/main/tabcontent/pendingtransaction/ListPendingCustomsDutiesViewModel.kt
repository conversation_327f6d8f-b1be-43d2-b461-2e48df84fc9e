package vn.com.bidv.feature.government.service.ui.list.tabcontent.pendingtransaction

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ListTransactionBaseReducer
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ListTransactionBaseViewModel
import vn.com.bidv.feature.government.service.domain.model.TxnPendingListDMO
import vn.com.bidv.feature.government.service.model.ListCustomsDutiesRuleFilter
import javax.inject.Inject

@HiltViewModel
class ListPendingCustomsDutiesViewModel @Inject constructor(
) : ListTransactionBaseViewModel<TxnPendingListDMO, ListCustomsDutiesRuleFilter>(
    initialState = ListTransactionBaseReducer.ListTransactionBaseViewState(),
    reducer = ListTransactionBaseReducer()
) {
    override fun handleEffect(
        sideEffect: ListTransactionBaseReducer.ListTransactionBaseViewEffect<TxnPendingListDMO>,
        onResult: (ListTransactionBaseReducer.ListTransactionBaseViewEvent<TxnPendingListDMO, ListCustomsDutiesRuleFilter>) -> Unit
    ) {
    }
}

