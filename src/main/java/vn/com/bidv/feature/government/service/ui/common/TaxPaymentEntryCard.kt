package vn.com.bidv.feature.government.service.ui.common

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMoreReducer
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.NoRuleFilters
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.model.ActionType
import vn.com.bidv.feature.government.service.common.Constants
import vn.com.bidv.feature.government.service.domain.model.InquiryCustomsDutyDMO
import vn.com.bidv.feature.government.service.model.IViewTransaction
import vn.com.bidv.feature.government.service.navigation.GovernmentServiceRoute

enum class TaxPaymentEntryCardActionScenario(val actions: List<ActionType>) {
    NONE(listOf()),
    PENDING(
        listOf(
            ActionType.Push_Approval,
            ActionType.EDIT,
            ActionType.Print_Document,
            ActionType.Delete,
        )
    ),
    ALL(listOf()),
}

@Composable
fun TaxPaymentEntryCard(
    transactionsDMO: ModelCheckAble<out IViewTransaction>,
    navController: NavHostController,
    actionScenario: TaxPaymentEntryCardActionScenario,
    onCheckedChange: () -> Unit,
) {
    val colorScheme = LocalColorScheme.current
    ItemCardCommonWithModifier(
        modifier = Modifier
            .clip(shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL))
            .background(colorScheme.bgMainTertiary)
            .border(
                width = 1.dp,
                color = colorScheme.borderMainSecondary,
                shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL)
            ),
        transactionsDMO = transactionsDMO,
        uiState = object : ViewState {},
        onEventListView = { it: ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent<IViewTransaction, NoRuleFilters> ->
            when (it) {
                is ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent.SelectItem -> {
                    onCheckedChange()
                }

                else -> {
                    // Do nothing
                }
            }
        },
        onClickItem = {
            if (transactionsDMO.data is InquiryCustomsDutyDMO) {
                navController.navigate(
                    GovernmentServiceRoute.TaxPaymentDetailRoute(
                        args = (transactionsDMO.data as InquiryCustomsDutyDMO)
                    )
                )
            }
        },

        listAction = actionScenario.actions,
        handleAction = { _ ->

        },
        contentHeader = { modelHeader ->
            ItemCardHeader(modelHeader.data)
        },
        contentBody = { modelBody ->
            ItemCardBody(modelBody.data)
        },
        shouldShowItemCheckBox = true,
    )
}

@Preview
@Composable
fun PrevTaxPaymentEntryCard() {
    Box(Modifier.background(Color.White).padding(4.dp)) {
        TaxPaymentEntryCard(
            transactionsDMO = ModelCheckAble(Constants.fakeTransactions[0]),
            navController = rememberNavController(),
            actionScenario = TaxPaymentEntryCardActionScenario.NONE,
            onCheckedChange = {}
        )
    }
}