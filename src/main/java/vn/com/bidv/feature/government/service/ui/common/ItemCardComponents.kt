package vn.com.bidv.feature.government.service.ui.common

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.government.service.model.IViewTransaction
import vn.com.bidv.sdkbase.utils.exts.dateToString
import vn.com.bidv.sdkbase.utils.exts.toDate
import vn.com.bidv.sdkbase.utils.formatAmount

@Composable
fun ItemCardHeader(
    model: IViewTransaction,
) {
    val style = LocalTypography.current
    val colorScheme = LocalColorScheme.current

    Row(
        modifier = Modifier
            .fillMaxWidth(0.9f),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Start
    ) {
        Text(
            modifier = Modifier
                .weight(1f, fill = false)
                .padding(start = IBSpacing.spacingXs),
            text = model.getHeaderString(),
            style = style.titleTitle_s,
            color = colorScheme.contentMainPrimary,
            maxLines = Int.MAX_VALUE,
            overflow = TextOverflow.Clip
        )
    }

}

@Composable
fun ItemCardBody(
    item: IViewTransaction,
) {
    val style = LocalTypography.current
    val colorScheme = LocalColorScheme.current

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                start = IBSpacing.spacingM, end = IBSpacing.spacingM, bottom = IBSpacing.spacingXs
            )
    ) {
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = item.getValueTitle(),
            style = style.titleTitle_s,
            color = colorScheme.contentMainPrimary,
        )

        Spacer(modifier = Modifier.height(IBSpacing.spacingS))

        Text(
            modifier = Modifier,
            text = item.getValueTaxDescription(),
            style = style.bodyBody_m,
            color = colorScheme.contentMainTertiary,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )

        Spacer(modifier = Modifier.height(IBSpacing.spacingS))

        Row(
            modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                modifier = Modifier.padding(end = IBSpacing.spacing2xs),
                text = item.getValueAmount().formatAmount(currCode = item.getValueCcy(), isShowCurrCode = true),
                style = style.titleTitle_m,
                color = colorScheme.contentMainPrimary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Spacer(modifier = Modifier.width(IBSpacing.spacingS))
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.End,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    modifier = Modifier.size(20.dp),
                    painter = painterResource(id = vn.com.bidv.designsystem.R.drawable.calendar_outline),
                    contentDescription = "",
                    tint = Color.Unspecified
                )
                Spacer(modifier = Modifier.width(IBSpacing.spacing2xs))
                val (datePattern, date) = item.getValueDate()
                Text(
                    modifier = Modifier,
                    text = date
                        .toDate(pattern = datePattern)
                        ?.dateToString() ?: "",
                    style = style.bodyBody_m,
                    color = colorScheme.contentMainTertiary,
                )
            }
        }
    }
}