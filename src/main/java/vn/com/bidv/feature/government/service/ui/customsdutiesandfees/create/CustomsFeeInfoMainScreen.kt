package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonSize
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.step2.CustomsFeeInfoStep2Screen
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.step3.CustomsFeeInfoStep3Screen
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step1.TaxPaymentInfoScreen

@Composable
fun CustomsFeeInfoMainScreen(navController: NavHostController) {

    val viewModel: CustomsFeeInfoMainViewModel = hiltViewModel()

    BaseMVIScreen(viewModel = viewModel) { uiState, uiEvent ->
        Box(modifier = Modifier.fillMaxSize()) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
            ) {
                when (uiState.currentStep) {
                    1 -> TaxPaymentInfoScreen(navController) {
                        StepProgressBar(uiState, uiEvent)
                    }
                    2 -> CustomsFeeInfoStep2Screen()
                    3 -> CustomsFeeInfoStep3Screen()
                }
            }
        }
    }
}

@Composable
fun BottomViewStep12(modifier: Modifier = Modifier) {
    Box(modifier = modifier) {
        Column(modifier = Modifier.padding(IBSpacing.spacingM)) {
            Row(
                modifier = Modifier
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Image(
                        modifier = Modifier.size(IBSpacing.spacing2xl),
                        imageVector = ImageVector.vectorResource(R.drawable.khoan_phai_thu),
                        contentDescription = null
                    )
                    Spacer(modifier = Modifier.width(IBSpacing.spacingXs))
                    Text(
                        modifier = Modifier,
                        text = stringResource(vn.com.bidv.localization.R.string.tong_tien) + ":",
                        style = LocalTypography.current.bodyBody_l,
                        color = LocalColorScheme.current.contentMainPrimary
                    )
                }

                Spacer(modifier = Modifier.width(IBSpacing.spacingM))

                Text(
                    modifier = Modifier,
                    text = "100,000,000,000,000 VND",
                    style = LocalTypography.current.titleTitle_l,
                    color = LocalColorScheme.current.contentMainPrimary
                )

            }


            Spacer(modifier = Modifier.height(IBSpacing.spacingM))

            IBankNormalButton(
                modifier = Modifier.fillMaxWidth(),
                size = NormalButtonSize.L(LocalTypography.current),
                type = NormalButtonType.PRIMARY(LocalColorScheme.current),
                text = stringResource(vn.com.bidv.localization.R.string.tiep_tuc)
            ) {}

        }
    }
}

@Preview(showBackground = true)
@Composable
fun BottomViewPreview() {

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(LocalColorScheme.current.bgMainPrimary)
    ) {
        Column(modifier = Modifier.padding(IBSpacing.spacingM)) {
            Row(
                modifier = Modifier
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Image(
                        modifier = Modifier.size(IBSpacing.spacing2xl),
                        imageVector = ImageVector.vectorResource(R.drawable.khoan_phai_thu),
                        contentDescription = null
                    )
                    Spacer(modifier = Modifier.width(IBSpacing.spacingXs))
                    Text(
                        modifier = Modifier,
                        text = stringResource(vn.com.bidv.localization.R.string.tong_tien) + ":",
                        style = LocalTypography.current.bodyBody_l,
                        color = LocalColorScheme.current.contentMainPrimary
                    )
                }

                Spacer(modifier = Modifier.width(IBSpacing.spacingM))

                Text(
                    modifier = Modifier,
                    text = "100,000,000,000 VND",
                    style = LocalTypography.current.titleTitle_l,
                    color = LocalColorScheme.current.contentMainPrimary
                )

            }


            Spacer(modifier = Modifier.height(IBSpacing.spacingM))

            IBankNormalButton(
                modifier = Modifier.fillMaxWidth(),
                size = NormalButtonSize.L(LocalTypography.current),
                type = NormalButtonType.PRIMARY(LocalColorScheme.current),
                text = stringResource(vn.com.bidv.localization.R.string.tiep_tuc),
                isEnable = false
            )
            { }

        }
    }
}
