package vn.com.bidv.feature.government.service.ui.list.tabcontent.alltransaction

import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.sdkbase.ui.EmptyViewModel

@Composable
fun ListAllCustomsDutiesScreen(navController: NavHostController) {
    val viewModel: EmptyViewModel = hiltViewModel()

    BaseScreen(
        navController = navController,
        viewModel = viewModel,
        topAppBarConfig = TopAppBarConfig(isShowTopAppBar = false)
    ) { _, _ ->

        Text("CustomDutiesAndFeesListAllTransactionScreen")
    }

}