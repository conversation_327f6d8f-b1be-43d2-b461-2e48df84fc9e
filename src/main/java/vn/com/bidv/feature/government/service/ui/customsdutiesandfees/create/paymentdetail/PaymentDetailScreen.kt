package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.paymentdetail

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.IBankSectionHeader
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.LeadingType
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankTopAppBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.government.service.domain.model.InquiryCustomsDutyDMO
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.formatMoney

@Composable
fun PaymentDetailScreen(
    inquiryData: InquiryCustomsDutyDMO,
    navController: NavHostController
) {
    val curLocalColorScheme = LocalColorScheme.current

    val topAppBarConfig = TopAppBarConfig(
        isShowTopAppBar = true,
        isShowNavigationIcon = true,
        titleTopAppBar = "Chi tiết khoản nộp",
        titleTopAppBarColor = curLocalColorScheme.bgNon_opaqueInverse_pressed,
        showHomeIcon = false
    )

    Scaffold(
        topBar = {
            IBankTopAppBar(navController, TopAppBarType.Title, topAppBarConfig)
        },
        contentWindowInsets = WindowInsets(0.dp, 0.dp, 0.dp, 0.dp),
        containerColor = curLocalColorScheme.bgMainSecondary
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(WindowInsets.navigationBars.asPaddingValues())
                .padding(IBSpacing.spacingM)
        ) {
            item {
                PaymentInfoSection(inquiryData)
                Spacer(modifier = Modifier.height(IBSpacing.spacingM))
            }

            item {
                BeneficiaryInfoSection(inquiryData)
                Spacer(modifier = Modifier.height(IBSpacing.spacingM))
            }
        }
    }
}

@Composable
private fun PaymentInfoSection(data: InquiryCustomsDutyDMO) {
    InfoCard(title = stringResource(R.string.thong_tin_khoan_nop)) {
        DetailItem(
            label = stringResource(R.string.so_to_khai_hai_quan),
            value = data.declarationNo
        )
        DetailItem(
            label = "Ngày tờ khai hải quan",
            value = data.declarationDate
        )
        DetailItem(
            label = "Mã chương",
            value = "${data.chapterCode} - ${data.chapterName ?: ""}"
        )
        DetailItem(
            label = "Mã loại hình xuất nhập khẩu",
            value = "${data.eiTypeCode} - ${data.eiTypeName ?: ""}"
        )
        DetailItem(
            label = "Mã nội dung kinh tế",
            value = "${data.ecCode} - ${data.ecName ?: ""}"
        )
        DetailItem(
            label = stringResource(R.string.so_tien),
            value = data.amount.formatMoney(data.ccy, isShowCurrCode = true)
        )
        DetailItem(
            label = "Diễn giải giao dịch",
            value = data.transDesc
        )
    }
}

@Composable
private fun BeneficiaryInfoSection(data: InquiryCustomsDutyDMO) {
    InfoCard(title = "Thông tin bên hưởng") {
        DetailItem(
            label = "Mã và tên kho bạc",
            value = "${data.treasuryCode} - ${data.treasuryName ?: ""}"
        )
        DetailItem(
            label = stringResource(R.string.co_quan_thu),
            value = "${data.revAuthCode} - ${data.revAuthName ?: ""}"
        )
        DetailItem(
            label = stringResource(R.string.dia_ban_hanh_chinh),
            value = "${data.admAreaCode} - ${data.admAreaName ?: ""}"
        )
        DetailItem(
            label = stringResource(R.string.tai_khoan_thu_nsnn),
            value = "${data.revAccCode} - ${data.revAccName ?: ""}"
        )
        DetailItem(
            label = stringResource(R.string.loai_hinh_nnt),
            value = data.payerTypeName ?: ""
        )
    }
}

@Composable
private fun InfoCard(
    title: String,
    body: @Composable () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = LocalColorScheme.current.bgMainTertiary,
                shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL)
            )
    ) {
        IBankSectionHeader(
            shLeadingType = LeadingType.Dash(),
            shSectionTitle = title,
        )

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = IBSpacing.spacingM)
        ) {
            body()
        }
    }
}

@Composable
private fun DetailItem(label: String, value: String) {
    val curLocalTypography = LocalTypography.current
    val curLocalColorScheme = LocalColorScheme.current

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = IBSpacing.spacingXs)
    ) {
        Text(
            text = label,
            style = curLocalTypography.bodyBody_m,
            color = curLocalColorScheme.contentMainTertiary
        )
        Text(
            text = value,
            style = curLocalTypography.titleTitle_m,
            color = curLocalColorScheme.contentMainPrimary
        )
    }
}

@Preview(showBackground = true, name = "Tax Payment Detail Screen Preview")
@Composable
fun PreviewTaxPaymentDetailScreen() {
    val mockData = InquiryCustomsDutyDMO(
        eiTypeCode = "A41",
        taxTypeCode = "001",
        ccCode = "VND",
        chapterCode = "AVG",
        ecCode = "AVG",
        amount = "87007000",
        ccy = "VND",
        declarationDate = "01/01/2025",
        declarationNo = "762023569553",
        transDesc = "Thanh toán thuế quốc tế",
        payerType = 1,
        ccName = "VND",
        chapterName = "782464",
        ecName = "Loại tiền",
        eiTypeName = "Loại hình",
        taxTypeName = "Thuế môi bài",
        treasuryCode = "001",
        treasuryName = "Kho bạc nhà nước Việt",
        admAreaCode = "003",
        admAreaName = "Khu vực 3",
        revAccCode = "--",
        revAccName = "--",
        revAuthCode = "003",
        revAuthName = "Cục thuế quận 3",
        payerTypeName = "Doanh nghiệp"
    )

    PaymentInfoSection(mockData)
}
