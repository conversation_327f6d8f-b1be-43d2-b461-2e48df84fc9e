package vn.com.bidv.ibank.domain

import vn.com.bidv.ibank.data.UpdateVersionRepository
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class UpdateVersionUseCase @Inject constructor(
    private val updateVersionRepository: UpdateVersionRepository
) {
    suspend fun updateVersion(): DomainResult<CurrentVersionResponseDMO> {
        val result = updateVersionRepository.updateVersion()
        val domain = result.convert(CurrentVersionResponseDMO::class.java)
        return domain
    }
}