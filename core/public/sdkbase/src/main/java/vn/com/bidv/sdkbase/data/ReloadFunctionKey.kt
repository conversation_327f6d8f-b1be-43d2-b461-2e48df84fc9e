package vn.com.bidv.sdkbase.data

enum class ReloadFunctionKey(val value : String) {
    LIST_PENDING("LIST_PENDING"),
    LIST_BULK("LIST_BULK"),
    LIST_SALARY("LIST_SALARY"),
    LIST_RECALL("LIST_RECALL"),
    LIST_INQUIRY("LIST_INQUIRY"),
    DETAIL("DETAIL"),
    LIST_DEPOSIT_APPROVE("LIST_DEPOSIT_APPROVE"),
    LIST_TEMPLATE("LIST_TEMPLATE"),
    TURN_ON_BIOMETRIC("TURN_ON_BIOMETRIC"),
    TURN_ON_BIOMETRIC_FAIL("TURN_ON_BIOMETRIC_FAIL"),
    EDIT_QUESTIONS("EDIT_QUESTIONS"),
    POSITIVE_CHANGE_PW("POSITIVE_CHANGE_PW"),
    LIST_FOREIGN_EXCHANGE("LIST_FOREIGN_EXCHANGE"),
    LIST_FOREIGN_EXCHANGE_PENDING_APPROVAL("LIST_FOREIGN_EXCHANGE_PENDING_APPROVAL")
}