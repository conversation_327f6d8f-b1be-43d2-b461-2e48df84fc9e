package vn.com.bidv.sdkbase.utils.constants

object SdkBaseConstants {

    object DateTimeConstants {
        const val FORMAT_DD_MM_YYYY = "dd/MM/yyyy"
        const val FORMAT_DD_MM_YYYY_HH_MM_SS = "dd/MM/yyyy HH:mm:ss"
        const val INPUT_FORMAT_DD_MM_YYYY_HH_MM_SS_SSSSSS = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS"
        const val INPUT_FORMAT_DD_MM_YYYY_HH_MM_SS_SSSSSSSSS = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS"
        const val INPUT_FORMAT_DD_MM_YYYY_HH_MM_SS = "yyyy-MM-dd'T'HH:mm:ss"
        const val FORMAT_YYYY_MM_DD = "yyyy-MM-dd"
        const val FORMAT_HHMMSS_DDMMYYY = "HH:mm:ss dd/MM/yyyy"
    }

    object INTENT_TYPE {
        const val APPLICATION = "application/"
        const val IMAGE = "image/"
        const val TEXT = "text/"
    }

    object NotificationConstants {
        const val MORE_INFO = "moreInfo"
        const val BLANK: String = ""
        const val REDIRECT_ID: String = "redirectId"
        const val NOTIFY_ID: String = "notifyId"
        const val NOTI_TYPE: String = "notiType"
        const val DISPLAY_TAB: String = "displayTab"
        const val PARAMS: String = "params"
        const val TAB_1: String = "tab1"
        const val TAB_2: String = "tab2"
        const val FUNCTION_CODE: String = "functionCode"
        const val CHANNEL_ID: String = "IBank2FcmChannel"
        const val MAIN_ACTIVITY_CLASS: String = "vn.com.bidv.ibank.MainActivity"
        const val FCM_TRIGGER_UPDATE_TOTAL_NOTIFICATION_UNREAD: String =
            "FCM_TRIGGER_UPDATE_TOTAL_NOTIFICATION_UNREAD"
    }

    object MoneyCurrencyConstants {
        const val AUD = "AUD"
        const val BND = "BND"
        const val BRL = "BRL"
        const val CAD = "CAD"
        const val CHF = "CHF"
        const val CNY = "CNY"
        const val CZK = "CZK"
        const val DKK = "DKK"
        const val EUR = "EUR"
        const val GBP = "GBP"
        const val HKD = "HKD"
        const val IDR = "IDR"
        const val INR = "INR"
        const val JPY = "JPY"
        const val KHR = "KHR"
        const val KRW = "KRW"
        const val KWD = "KWD"
        const val LAK = "LAK"
        const val MXN = "MXN"
        const val MYR = "MYR"
        const val NOK = "NOK"
        const val NZD = "NZD"
        const val PHP = "PHP"
        const val PLN = "PLN"
        const val RUB = "RUB"
        const val SEK = "SEK"
        const val SGD = "SGD"
        const val THB = "THB"
        const val TWD = "TWD"
        const val USD = "USD"
        const val VND = "VND"
        const val QAR = "QAR"
        const val ZAR = "ZAR"
        const val LKR = "LKR"
        const val XAU = "XAU"
        const val AED = "AED"
        const val CLP = "CLP"
    }

}