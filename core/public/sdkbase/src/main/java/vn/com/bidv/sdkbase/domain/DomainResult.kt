package vn.com.bidv.sdkbase.domain

import com.google.gson.JsonElement
import vn.com.bidv.network.NetworkException
import vn.com.bidv.network.NetworkStatusCode

sealed class DomainResult<out T> {

    fun getSafeData() : T? {
        return when (this) {
            is Success -> data
            else -> null
        }
    }

    data class Success<T>(val data: T?) : DomainResult<T>()
    data class Error(
        val errorCode: String,
        val errorMessage: String? = null,
        val listClientErrorCode: List<Pair<String, String>>? = null, // first: ViewID error, second: Error Code
        val data: JsonElement? = null,
        val status: String? = null
    ) : DomainResult<Nothing>() {
        fun isSessionExpired(): Boolean {
            return errorCode == NetworkException.getHttpCodeSessionExpired()
        }

        fun isHttpError(code: String) : Boolean {
            return errorCode.startsWith(NetworkStatusCode.HTTP_CODE_PREFIX)
                    && errorCode.contains(code)
        }
    }
}