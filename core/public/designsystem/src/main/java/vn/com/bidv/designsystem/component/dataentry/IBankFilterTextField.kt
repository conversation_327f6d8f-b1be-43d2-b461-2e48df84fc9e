package vn.com.bidv.designsystem.component.dataentry

import java.text.Normalizer
import java.util.regex.Pattern
import kotlin.math.max

interface IBankFilterTextField {
    fun apply(input: String): String
}

/**
 * A filter that removes special characters from a given input string.
 *
 * This class implements the `IBankFilterTextField` interface and is used to sanitize text input
 * by removing all characters that are not letters, digits, or whitespace.
 *
 * Example usage:
 * ```
 * val filter = SpecialCharacterFilter()
 * val filtered = filter.apply("Hello, World! @2023")
 * // Result: "Hello World 2023"
 * ```
 */
class SpecialCharacterFilter : IBankFilterTextField {
    override fun apply(input: String): String {
        return input.replace(Regex("[^\\p{L}\\p{Nd}\\s]"), "")
    }
}

/**
 * A filter that removes Vietnamese accents (diacritical marks) from a given input string.
 *
 * This class implements the `IBankFilterTextField` interface and is used to normalize text by
 * converting accented Vietnamese characters into their unaccented equivalents.
 *
 * Example usage:
 * ```
 * val filter = RemoveVietnameseAccentFilter()
 * val filtered = filter.apply("Thành phố Hồ Chí Minh")
 * // Result: "Thanh pho Ho Chi Minh"
 * ```
 */
class RemoveVietnameseAccentFilter : IBankFilterTextField {
    override fun apply(input: String): String {
        val normalized = Normalizer.normalize(input, Normalizer.Form.NFD)
        return Pattern.compile("\\p{InCombiningDiacriticalMarks}+").matcher(normalized)
            .replaceAll("")
    }
}

// Filter Remove Number
class NumberFilter : IBankFilterTextField {
    override fun apply(input: String): String {
        return input.replace(Regex("\\d"), "")
    }
}

/**
 * A filter that formats an input string into a currency format based on the specified currency code.
 *
 * @property currencyCode The currency code (e.g., "USD", "VND") used to format the money.
 *
 * This class implements the `IBankFilterTextField` interface and is responsible for processing
 * an input string by:
 * 1. Extracting only numeric characters from the input.
 * 2. Formatting the extracted number into a currency string using the specified currency code.
 *
 * Example usage:
 * ```
 * val formatter = FormatCurrency("USD")
 * val formatted = formatter.apply("123456")
 * // Result: "$123,456.00"
 * ```
 */
class FormatCurrency(
    private val pattern: Pattern = Pattern.VND,
    // maxLength: represent for maxLength of integer part
    private val maxLength: Int? = null,
) : IBankFilterTextField {
    override fun apply(input: String): String {
        if (maxLength != null) {
            val length = input.replace(",", "")
                .replace(".", "")
                .length
            if (length > maxLength) {
                val newInput = StringBuilder()
                var count = 0
                input.forEach {
                    if (it.isDigit()) {
                        newInput.append(it)
                        count++
                    } else if (it == ',' || it == '.') {
                        newInput.append(it)
                    }
                }
                return format(newInput.toString())
            }
            return format(input)
        } else {
            return format(input)
        }
    }

    private fun format(input: String): String {
        if (input.isBlank()) {
            return ""
        }
        if (input.endsWith(",")) {
            return input.dropLast(1)
        }
        val newInput = input.replace(",", "")

        val regex = Regex("(\\d)(?=(\\d{3})+\$)")
        if (pattern == Pattern.USD) {
            if (input == ".") {
                return ""
            }
            val amount = if (newInput == "0") {
                "0"
            } else {
                var result = removeLeadingZero(newInput)
                if (result.isBlank()) {
                    result = "0"
                }
                result
            }
            val parts = amount.split(".")
            var integerPart = parts[0]
            val decimalPart = parts.getOrNull(1)?.take(2)

            maxLength?.let {
                if (integerPart.length > maxLength) {
                    integerPart = integerPart.take(maxLength)
                }
            }

            val formattedIntegerPart = regex.replace(integerPart) { "${it.value}," }

            return when {
                decimalPart == null -> formattedIntegerPart
                decimalPart.isEmpty() -> "$formattedIntegerPart."
                else -> "$formattedIntegerPart.${decimalPart.take(2)}"
            }
        } else {
            val amount =
                removeLeadingZero(newInput).replace(".", "").take(maxLength ?: Int.MAX_VALUE)
            return regex.replace(amount) { "${it.value}," }
        }
    }

    private fun removeLeadingZero(input: String): String {
        val regex = Regex("^0+(?!\\.)") // Matches leading zeros not followed by a dot (decimal)
        return regex.replaceFirst(input, "")
    }

    enum class Pattern {
        VND, USD
    }

}

class RemoveSpaceFilter : IBankFilterTextField {
    override fun apply(input: String): String {
        return input.replace(Regex("\\s"), "")
    }
}

class InputFieldMoneyFilter : IBankFilterTextField {
    override fun apply(input: String): String {
        val regex = Regex("[^0-9.,]")
        return input.replace(regex, "")
    }
}

class RemoveSpecialCharacterFilterCNR : IBankFilterTextField {
    override fun apply(input: String): String {
        val normalized = Normalizer.normalize(input, Normalizer.Form.NFD)
        val regex = Regex("[^0-9a-zA-Z/\\\\.,\\-_\\s]")
        val inputWithRemoveVietnameseAccent =
            Pattern.compile("\\p{InCombiningDiacriticalMarks}+").matcher(normalized).replaceAll("")
        return inputWithRemoveVietnameseAccent.replace(regex, "")
    }
}

class RemoveSpecialCharacterRefuseDeposit : IBankFilterTextField {
    override fun apply(input: String): String {
        val normalized = Normalizer.normalize(input, Normalizer.Form.NFD)
        val regex = Regex("[^0-9a-zA-Z\\\\s]")
        val inputWithRemoveVietnameseAccent =
            Pattern.compile("\\p{InCombiningDiacriticalMarks}+").matcher(normalized).replaceAll("")
        return inputWithRemoveVietnameseAccent.replace(regex, "")
    }
}

class RemoveSpecialCharacterFieldPayerIDCNR : IBankFilterTextField {
    override fun apply(input: String): String {
        val normalized = Normalizer.normalize(input, Normalizer.Form.NFD)
        val regex = Regex("[^0-9a-zA-Z/\\\\.,\\-_]")
        val inputWithRemoveVietnameseAccent =
            Pattern.compile("\\p{InCombiningDiacriticalMarks}+").matcher(normalized).replaceAll("")
        return inputWithRemoveVietnameseAccent.replace(regex, "")
    }
}

class RemoveSpecialCharacterPasswordFilter : IBankFilterTextField {
    override fun apply(input: String): String {
        val allowedCharsRegex = "[^a-zA-Z0-9!@#\$%^&]".toRegex()
        return input.replace(allowedCharsRegex, "")
    }
}

/**
 * A filter that removes consecutive spaces from a given input string, replacing them with a single space.
 *
 * This class implements the `IBankFilterTextField` interface and is used to normalize text input
 * by ensuring that there are no multiple consecutive spaces.
 *
 * Example usage:
 * ```
 * val filter = RemoveDoubleSpaceFilter()
 * val filtered = filter.apply("  This is a    test.   ")
 * // Result: "This is a test."
 * ```
 */
class RemoveDoubleSpaceFilter : IBankFilterTextField {
    override fun apply(input: String): String {
        return input.trimStart().replace(Regex(" {2,}"), " ")
    }
}

/**
 * A filter that allows only specific characters in the input string.
 *
 * @property specialCharacters The set of special characters to allow (e.g., "-?:;.,/!@#\$%^&*+_{} ").
 *
 * This class implements the `IBankFilterTextField` interface and is responsible for processing
 * an input string by removing all characters that are not letters, digits, or part of the specified set of special characters.
 *
 * Example usage:
 * ```
 * val filter = AllowSpecificCharactersFilter()
 * val filtered = filter.apply("Hello, World! @2023")
 * // Result: "Hello, World! @2023"
 * ```
 */
class AllowSpecificCharactersFilter(private val specialCharacters: String = "-?:;.,/!@#\$%^&*+_{} ") :
    IBankFilterTextField {
    override fun apply(input: String): String {
        val allowedCharsRegex = "[^a-zA-Z0-9${Regex.escape(specialCharacters)}]".toRegex()
        return input.replace(allowedCharsRegex, "")
    }
}

/**
 * A filter that allows only alphabetic characters in the input string with Special Character.
 *
 * @property specialCharacters The set of special characters to allow (e.g., " ").
 *
 * This class implements the `IBankFilterTextField` interface and is responsible for processing
 * an input string by removing all characters that are not letters, or part of the specified set of special characters.
 *
 * Example usage:
 * ```
 * val filter = AlphabeticCharactersFilter()
 * val filtered = filter.apply("Hello, World! @2023")
 * // Result: "Hello World"
 * ```
 */
class AlphabeticCharactersFilter(private val specialCharacters: String = " ") :
    IBankFilterTextField { // todo binhnv update remove vnchar later
    override fun apply(input: String): String {
        val allowedCharsRegex = "[^a-zA-Z${Regex.escape(specialCharacters)}]".toRegex()
        return input.replace(allowedCharsRegex, "")
    }
}

/**
 * A filter that uppercases all characters in the input string.
 * eg: "hello world" -> "HELLO WORLD"
 */
class UpperCaseFilter : IBankFilterTextField {
    override fun apply(input: String): String {
        return input.uppercase()
    }
}

class CustomerCodeFilter : IBankFilterTextField {
    override fun apply(input: String): String {
        val allowedCharsRegex = "[^a-zA-Z0-9,.\\-_]".toRegex()
        return input.replace(allowedCharsRegex, "")
    }
}

class TextAndNumberFilter : IBankFilterTextField {
    override fun apply(input: String): String {
        val allowedCharsRegex = "[^a-zA-Z0-9\\s]".toRegex()
        return input.replace(allowedCharsRegex, "")
    }
}

class TextOnlyFilter : IBankFilterTextField {
    override fun apply(input: String): String {
        val allowedCharsRegex = "[^a-zA-Z\\s]".toRegex()
        return input.replace(allowedCharsRegex, "")
    }
}

class ContentFilter : IBankFilterTextField {
    override fun apply(input: String): String {
        val formatSpace = input.replace(Regex("[\t\r\n]+"), " ")
        val allowedCharsRegex = "[^a-zA-Z0-9\\s,.-]".toRegex()
        return formatSpace.replace(allowedCharsRegex, "")
    }
}

class RemoveTabEnterFilter : IBankFilterTextField {
    override fun apply(input: String): String {
        val formatSpace = input.replace(Regex("[\t\r\n]+"), " ")
        return formatSpace
    }
}

/**
 * A filter that formats an input string into a email format.
 *
 * This class implements the `IBankFilterTextField` interface and is used to normalize email by
 * allowing only numeric characters, non-accented letters, and the special characters @, _, and -.
 *
 * Example usage:
 * ```
 * val filter = InputFieldEmailFilter()
 * val filtered = filter.apply("bidv$$$@123!")
 * // Result: "bidv@123"
 * ```
 */
class InputFieldEmailFilter : IBankFilterTextField {
    override fun apply(input: String): String {
        return input.replace(Regex("[^a-zA-Z0-9@_.-]"), "")
    }
}

/**
 * A filter that removes letters characters from a given input string.
 *
 * This class implements the `IBankFilterTextField` interface and is used to sanitize text input
 * by removing all characters that are letters.
 *
 * Example usage:
 * ```
 * val filter = RemoveTextFilter()
 * val filtered = filter.apply("123 BIDV")
 * // Result: "123 "
 * ```
 */
class RemoveTextFilter : IBankFilterTextField {
    override fun apply(input: String): String {
        val normalized = Normalizer.normalize(input, Normalizer.Form.NFD)
        val textVietnameseAccentFilter =
            Pattern.compile("\\p{InCombiningDiacriticalMarks}+").matcher(normalized)
                .replaceAll("")
        return textVietnameseAccentFilter.replace(Regex("[a-zA-Z]"), "")
    }
}
