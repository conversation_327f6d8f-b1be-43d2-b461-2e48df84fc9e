package vn.com.bidv.designsystem.component

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties

@Composable
fun IBankLoaderIndicatorsDialog(
    modifier: Modifier = Modifier,
    loadingSize: LoadingSize = LoadingSize.MEDIUM,
    backgroundColor: Color = Color.Black.copy(alpha = 0.3f),
    onDismissRequest: () -> Unit
) {
    val dialogProperties = DialogProperties(
        dismissOnBackPress = false,
        dismissOnClickOutside = false,
        usePlatformDefaultWidth = false
    )

    Dialog(onDismissRequest = onDismissRequest, properties = dialogProperties) {
        Box(
            modifier = modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            IBankLoaderIndicators(
                loadingSize = loadingSize,
                isParentFullSize = true,
                backgroundColor = backgroundColor
            )
        }
    }


}