package vn.com.bidv.designsystem.component.dataentry

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

data class IBankTextAndTextSupportConfig(
    val title: String = "",
    val content: String = "",
    val highlights: String = "",
    val textAlignLeft: Boolean = true,
    val hyperLinkOnclick: () -> Unit = {},
    val titleColor: Color? = null,
    val titleStyle: TextStyle? = null,
    val contentColor: Color? = null,
    val contentStyle: TextStyle? = null,
    val highlightsColor: Color? = null,
    val highlightsStyle: TextStyle? = null,
)

@Composable
fun IBankTextAndTextSupport(
    modifier: Modifier = Modifier,
    iBankTextAndTextSupportConfig: IBankTextAndTextSupportConfig
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    Column(
        modifier = modifier,
        horizontalAlignment = if (iBankTextAndTextSupportConfig.textAlignLeft) Alignment.End else Alignment.Start,
    ) {
        if (iBankTextAndTextSupportConfig.highlights.isEmpty()) {
            if (iBankTextAndTextSupportConfig.title.isNotEmpty()) {
                Text(
                    text = iBankTextAndTextSupportConfig.title,
                    style = iBankTextAndTextSupportConfig.titleStyle ?: typography.titleTitle_s,
                    color = iBankTextAndTextSupportConfig.titleColor ?: colorScheme.contentMainSecondary,
                    textAlign = if (iBankTextAndTextSupportConfig.textAlignLeft) TextAlign.End else TextAlign.Start,
                )
            }
            if (iBankTextAndTextSupportConfig.content.isNotEmpty()) {
                if (iBankTextAndTextSupportConfig.title.isNotEmpty()) Spacer(
                    Modifier.height(IBSpacing.spacing2xs)
                )
                Text(
                    text = iBankTextAndTextSupportConfig.content,
                    style = iBankTextAndTextSupportConfig.contentStyle ?: typography.bodyBody_m,
                    color = iBankTextAndTextSupportConfig.contentColor ?: colorScheme.contentMainTertiary,
                    textAlign = if (iBankTextAndTextSupportConfig.textAlignLeft) TextAlign.End else TextAlign.Start,
                )
            }
        } else {
            AnnotatedTextSupport(
                manualText = iBankTextAndTextSupportConfig.title,
                highlights = iBankTextAndTextSupportConfig.highlights,
                titleStyle = iBankTextAndTextSupportConfig.titleStyle,
                titleColor = iBankTextAndTextSupportConfig.titleColor,
                highlightsStyle = iBankTextAndTextSupportConfig.highlightsStyle,
                highlightsColor = iBankTextAndTextSupportConfig.highlightsColor,
            ) {
                iBankTextAndTextSupportConfig.hyperLinkOnclick()
            }
        }
    }
}

@Composable
private fun AnnotatedTextSupport(
    modifier: Modifier = Modifier,
    manualText: String,
    highlights: String,
    titleStyle: TextStyle ? = null,
    titleColor: Color? = null,
    highlightsStyle: TextStyle ? = null,
    highlightsColor: Color? = null,
    hyperLinkOnclick: () -> Unit = {}
) {
    // Display multiple links in the text
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current

    val fullString = "$manualText $highlights"
    val startIndex = fullString.indexOf(highlights)
    val endIndex = startIndex + highlights.length

    val buildAnnotatedString = buildAnnotatedString {
        append(fullString)
        addLink(
            url = LinkAnnotation.Url(
                "",
                TextLinkStyles(
                    style = SpanStyle(
                        color = highlightsColor ?: colorScheme.contentBrand_01Primary,
                        textDecoration = highlightsStyle?.textDecoration ?: TextDecoration.Underline,
                        fontStyle = highlightsStyle?.fontStyle,
                        fontFamily = highlightsStyle?.fontFamily,
                        fontWeight = highlightsStyle?.fontWeight,
                        letterSpacing = highlightsStyle?.letterSpacing ?: TextUnit.Unspecified,
                    )
                )

            ),
            start = startIndex,
            end = endIndex
        )

    }

    Text(
        text = buildAnnotatedString,
        style = titleStyle ?: typography.titleTitle_s,
        color = titleColor ?: colorScheme.contentMainSecondary,
        textAlign = TextAlign.Start,
        modifier = modifier.clickable {
            buildAnnotatedString
                .getLinkAnnotations(startIndex, endIndex)
                .firstOrNull()?.let {
                    hyperLinkOnclick()
                }
        },
    )

}

@Preview
@Composable
fun PreviewText1() {
    Column {
        IBankCheckBoxWithText(
            title = "Tôi xác nhận đã đọc và đồng ý với",
            content = "Điều khoản và dịch vụ",
            textAlignLeft = false
        )
        Spacer(Modifier.height(16.dp))

        IBankCheckBoxWithText(
            title = "Tôi xác nhận đã đọc và đồng ý với",
            content = "",
            textAlignLeft = true
        )

        IBankCheckBoxWithText(
            title = "Tôi xác nhận đã đọc và đồng ý với",
            content = "",
            highlights = "Điều khoản và dịch vụ",
            textAlignLeft = false,
        )
        Spacer(Modifier.height(16.dp))
    }
}
