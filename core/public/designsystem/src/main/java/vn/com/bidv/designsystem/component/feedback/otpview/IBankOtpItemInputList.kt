package vn.com.bidv.designsystem.component.feedback.otpview

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalTypography

@Composable
fun IBankOtpItemInputList(
    modifier: Modifier = Modifier,
    otpText: String = "",
    otpItemNumber: Int,
    isFocus: Boolean = true,
    otpTextStyle: TextStyle = LocalTypography.current.headlineHeadline_s,
    onOtpInputDone: (String) -> Unit = {},
) {
    var otp = ""
    var prevOtp by rememberSaveable { mutableStateOf("") }
    Row(modifier.testTagIBank("IBankOtpItemInputList_otp_value")) {
        otp = if (otpText.length > otpItemNumber) {
            otpText.substring(0, otpItemNumber)
        } else {
            otpText
        }
        for (index in 0..<otpItemNumber) {
            val isSelected = if (isFocus) {
                if (otp.length == otpItemNumber) {
                    index == otpItemNumber - 1
                } else {
                    index == otp.length
                }
            } else {
                false
            }
            OtpItemInput(
                number = otp.getOrNull(index)?.toString() ?: "",
                isSelected = isSelected,
                modifier = Modifier.weight(1f),
                textStyle = otpTextStyle
            )
            if (index < otpItemNumber - 1) {
                Spacer(modifier = Modifier.width(IBSpacing.spacingXs))
            }
        }
    }
    if (prevOtp != otp) {
        prevOtp = otp
        if (otp.length == otpItemNumber) {
            onOtpInputDone(otp)
        }
    }

}

@Preview
@Composable
fun test() {
    IBankOtpItemInputList(
        otpItemNumber = 6,
        otpText = "12345",
        modifier = Modifier
            .fillMaxWidth()
            .height(56.dp),
    ) {}
}