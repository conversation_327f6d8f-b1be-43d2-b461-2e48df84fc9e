package vn.com.bidv.designsystem.component.dataentry

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBShadow
import vn.com.bidv.designsystem.theme.IBShadow.dropShadow
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme

@Composable
fun IBankSwitchWithText(
    modifier: Modifier = Modifier,
    disable: Boolean = false,
    textAlignLeft: Boolean = true,
    checked: Boolean = false,
    title: String = "",
    content: String = "",
    scaleSize: Float = 0.7f,
    onCheckedChange: ((Boolean) -> Unit)
) {

    Row {
        if (title.isNotEmpty() || content.isNotEmpty()) {
            if (textAlignLeft) {
                Box(
                    Modifier.padding(end = IBSpacing.spacing2xs, top = IBSpacing.spacingM)
                ) {
                    IBankTextAndTextSupport(
                        iBankTextAndTextSupportConfig = IBankTextAndTextSupportConfig(
                            title = title,
                            content = content,
                            textAlignLeft = true
                        )
                    )
                }
            }
        }

        IBankSwitch(
            modifier = modifier,
            disable = disable,
            checked = checked,
            scaleSize = scaleSize
        ) { // Default
            onCheckedChange(it)
            // On Switch Change
        }

        if (title.isNotEmpty() || content.isNotEmpty()) {
            if (!textAlignLeft) {
                Box(
                    Modifier.padding(start = IBSpacing.spacing2xs, top = IBSpacing.spacingM)
                ) {
                    IBankTextAndTextSupport(
                        iBankTextAndTextSupportConfig = IBankTextAndTextSupportConfig(
                            title = title,
                            content = content,
                            textAlignLeft = false
                        )
                    )
                }
            }
        }
    }
}

@Composable
fun IBankSwitch(
    modifier: Modifier = Modifier,
    disable: Boolean = false,
    checked: Boolean = false,
    onColor: Boolean = false,
    scaleSize: Float = 0.7f,
    onCheckedChange: ((Boolean) -> Unit)
) {
    val colorScheme = LocalColorScheme.current
    val colorThumb: Color = if (!disable && checked) {
        colorScheme.contentOn_specialPrimary
    } else if (disable && checked) {
        colorScheme.contentOn_specialPrimary.copy(alpha = 0.4f)
    } else {
        colorScheme.contentDisableSecondary
    }

    Box(
        modifier = modifier,
        contentAlignment = Alignment.TopStart
    ) {
        Switch(
            modifier = Modifier
                .testTagIBank("IBankSwitch")
                .scale(scaleSize),
            enabled = (!disable),
            checked = checked,
            onCheckedChange = {
                onCheckedChange(it)
            },
            thumbContent = {
                Box(
                    modifier = Modifier
                        .dropShadow(
                            config = IBShadow.shadowsShadowm0.copy(
                                shape = RoundedCornerShape(
                                    IBCornerRadius.cornerRadiusRound
                                )
                            )
                        )
                        .dropShadow(
                            config = IBShadow.shadowsShadowm1.copy(
                                shape = RoundedCornerShape(
                                    IBCornerRadius.cornerRadiusRound
                                )
                            )
                        )
                ) {
                    Box(
                        modifier = Modifier
                            .width(24.dp)
                            .height(24.dp)
                            .clip(shape = RoundedCornerShape(IBCornerRadius.cornerRadiusRound))
                            .background(colorThumb)
                    )
                }
            },
            colors = SwitchDefaults.colors(
                checkedTrackColor = if (onColor) colorScheme.contentBrand_01Tertiary else colorScheme.contentBrand_01Primary,
                uncheckedTrackColor = colorScheme.contentPlaceholder,
                disabledCheckedTrackColor = colorScheme.bgBrand_01Tertiary,
                disabledUncheckedTrackColor = colorScheme.bgDisablePrimary,
                checkedBorderColor = if (onColor) colorScheme.contentBrand_01Tertiary else colorScheme.contentBrand_01Primary,
                uncheckedBorderColor = colorScheme.contentPlaceholder,
                disabledCheckedBorderColor = colorScheme.bgBrand_01Tertiary,
                disabledUncheckedBorderColor = colorScheme.bgDisablePrimary,
                checkedThumbColor = Color.Transparent,
                uncheckedThumbColor = Color.Transparent,
            ),
        )
    }
}

@Preview
@Composable
fun PreviewSwitch() {
    var checked by remember { mutableStateOf(false) }

    Column {
        Row {
            IBankSwitchWithText(
                title = "UnCheckbox Text Left",
                checked = checked,
                content = "Save my login details for next time",
                textAlignLeft = true
            ) {
                checked = it
            }
        }

        Row {
            IBankSwitchWithText(
                title = "Checkbox Text Right",
                content = "Save my login details for next time",
                checked = true,
                textAlignLeft = false
            ) {}
        }

        IBankSwitchWithText(
            // Custom
            disable = true,
            checked = true,
        ) {
            // On Switch Change
        }

        IBankSwitchWithText(
            // Custom
            disable = true,
            checked = false,
        ) {
            // On Switch Change
        }

        IBankSwitchWithText( // Custom
            disable = false,
            checked = true,
            title = "On Color Check"
        ) {
            // On Switch Change
        }

        IBankSwitchWithText( // Custom
            disable = false,
            checked = false,
            title = "On Color UnCheck"
        ) {
            // On Switch Change
        }

        IBankSwitchWithText( // Custom
            disable = false,
            checked = false,
            title = "On Color Disable"
        ) {
            // On Switch Change
        }
    }

}