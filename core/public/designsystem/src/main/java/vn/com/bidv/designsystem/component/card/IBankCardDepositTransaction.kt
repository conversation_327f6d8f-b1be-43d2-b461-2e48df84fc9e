package vn.com.bidv.designsystem.component.card

import IBGradient
import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.localization.R as RLocalization

@Composable
fun IBankCardDepositTransaction(
    modifier: Modifier = Modifier,
    @DrawableRes iconRes: Int? = null,
    iconModifier: Modifier = Modifier,
    prodLineName: String,
    moneyLabel: String,
    formattedMoneyStr: String,
    columnContents: Triple<Pair<String, String>, Pair<String, String>, Pair<String, String>>,
    onClickAction: (() -> Unit)? = null
) {
    Box(
        modifier = Modifier
            .background(
                brush = IBGradient.color_grd_card_primary,
                shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL)
            )
            .clickable(enabled = onClickAction != null, onClick = { onClickAction?.invoke() })
            .then(modifier)
    ) {
        Image(
            imageVector = ImageVector.vectorResource(id = R.drawable.bidv_flower_background_inquiry_deposit),
            contentDescription = null,
            modifier = Modifier
                .align(Alignment.TopEnd)
                .matchParentSize()
        )

        Column(
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.padding(vertical = IBSpacing.spacingS)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Spacer(modifier = Modifier.width(IBSpacing.spacingS))

                iconRes?.let {
                    Image(
                        painter = painterResource(iconRes),
                        contentDescription = null,
                        modifier = Modifier
                            .size(32.dp)
                            .background(
                                color = LocalColorScheme.current.bgMainTertiary,
                                shape = RoundedCornerShape(IBCornerRadius.cornerRadiusRound)
                            ).then(iconModifier)
                    )
                    Spacer(modifier = Modifier.width(IBSpacing.spacing2xs))
                }

                Text(
                    text = prodLineName,
                    color = LocalColorScheme.current.contentOn_specialPrimary,
                    style = LocalTypography.current.titleTitle_m,
                )
            }
            Spacer(modifier = Modifier.height(IBSpacing.spacingS))

            Text(
                text = moneyLabel,
                color = LocalColorScheme.current.contentOn_specialPrimary,
                style = LocalTypography.current.bodyBody_m,
                modifier = Modifier.padding(start = IBSpacing.spacingM)
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacing3xs))
            Text(
                text = formattedMoneyStr,
                color = LocalColorScheme.current.contentOn_specialPrimary,
                style = LocalTypography.current.headlineHeadline_s,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = IBSpacing.spacingM)
            )

            Spacer(Modifier.height(IBSpacing.spacingS))

            IBankDashedLine(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = IBSpacing.spacingM)
            )

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = IBSpacing.spacingM, top = IBSpacing.spacingM)
            ) {
                columnContents.toList().forEach {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = it.first,
                            color = LocalColorScheme.current.contentOn_specialSecondary,
                            style = LocalTypography.current.bodyBody_s,
                        )
                        Spacer(Modifier.height(IBSpacing.spacing3xs))
                        Text(
                            text = it.second,
                            color = LocalColorScheme.current.contentOn_specialPrimary,
                            style = LocalTypography.current.labelLabel_m,
                        )
                    }
                }

            }
        }
    }
}

@Preview
@Composable
internal fun IBankCardDepositTransactionPreview() {
    IBankCardDepositTransaction(
        modifier = Modifier.width(360.dp),
        iconRes = R.drawable.tien_gui_linh_hoat,
        iconModifier = Modifier.padding(IBSpacing.spacing2xs),
        prodLineName = "Tiền gửi linh hoạt",
        moneyLabel = "Số tiền gửi",
        formattedMoneyStr = "1,000,000 VND",
        columnContents = Triple(
            Pair(stringResource(RLocalization.string.ngay_den_han), "12/12/2021"),
            Pair(stringResource(RLocalization.string.lai_suat), "5.00%/năm"),
            Pair(stringResource(RLocalization.string.ky_han), "36 tháng"),
        )
    )
}