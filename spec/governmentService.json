{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://just4dev.dvc.ibank2dev.com", "description": "Generated server url"}], "paths": {"/gov/par/treasury/list/1.0": {"post": {"tags": ["governmentService"], "summary": "Treasury list", "description": "Treasury list", "operationId": "listTreasury", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTreasuryRes"}}}}}}}, "/gov/par/tax-type/list/1.0": {"post": {"tags": ["governmentService"], "summary": "Tax type list", "description": "Tax type list", "operationId": "listTaxType", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTaxTypeRes"}}}}}}}, "/gov/par/revenue-authority/list/1.0": {"post": {"tags": ["governmentService"], "summary": "Revenue authority list", "description": "Revenue authority list", "operationId": "listRevenueAuthority", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RevenueAuthorityReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListRevenueAuthorityRes"}}}}}}}, "/gov/par/revenue-account/list/1.0": {"post": {"tags": ["governmentService"], "summary": "Revenue account list", "description": "Revenue account list", "operationId": "listRevenueAccount", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListRevenueAccountRes"}}}}}}}, "/gov/par/export-import-type/list/1.0": {"post": {"tags": ["governmentService"], "summary": "Export import type list", "description": "Export import type list", "operationId": "listExportImportType", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListExportImportType"}}}}}}}, "/gov/par/economic-content/list/1.0": {"post": {"tags": ["governmentService"], "summary": "Economic content list", "description": "Economic content list", "operationId": "listEconomicContent", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListEconomicContentRes"}}}}}}}, "/gov/par/customs-currency/list/1.0": {"post": {"tags": ["governmentService"], "summary": "Customs currency list", "description": "Customs currency list", "operationId": "listCustomsCurrency", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListCustomsCurrencyRes"}}}}}}}, "/gov/par/chapter/list/1.0": {"post": {"tags": ["governmentService"], "summary": "Chapter list", "description": "Chapter list", "operationId": "listChapter", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListChapterRes"}}}}}}}, "/gov/par/administrative-area/list/1.0": {"post": {"tags": ["governmentService"], "summary": "Administrative area list", "description": "Administrative area list", "operationId": "listAdministrativeArea", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdministrativeAreaReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListAdministrativeAreaRes"}}}}}}}, "/gov/customs-duty/txn/save/1.0": {"post": {"tags": ["governmentService"], "summary": "Save transaction", "description": "Save transaction", "operationId": "save", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnSaveReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/gov/customs-duty/txn/pending/list/1.0": {"post": {"tags": ["governmentService"], "summary": "List pending transaction", "description": "List pending transaction", "operationId": "listPending", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnPendingListReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTxnPendingListRes"}}}}}}}, "/gov/customs-duty/txn/list/1.0": {"post": {"tags": ["governmentService"], "summary": "List all transaction", "description": "List all transaction", "operationId": "list", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnListReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTxnListRes"}}}}}}}, "/gov/customs-duty/txn/detail/1.0": {"post": {"tags": ["governmentService"], "summary": "Detail transaction", "description": "Detail transaction", "operationId": "detail", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnDetailReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnDetailRes"}}}}}}}, "/gov/customs-duty/txn/delete/1.0": {"post": {"tags": ["governmentService"], "summary": "Delete transaction", "description": "Delete transaction", "operationId": "delete", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnDeleteReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnDeleteRes"}}}}}}}, "/gov/customs-duty/template/save/1.0": {"post": {"tags": ["governmentService"], "summary": "Save template", "description": "Save template", "operationId": "save_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateSaveReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/gov/customs-duty/template/list/1.0": {"post": {"tags": ["governmentService"], "summary": "List template", "description": "List template", "operationId": "list_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnTemplateListReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTxnTemplateListRes"}}}}}}}, "/gov/customs-duty/tax/validate/1.0": {"post": {"tags": ["governmentService"], "summary": "Validate customs duty", "description": "Validate customs duty", "operationId": "validate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateCustomsDutyReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateCustomsDutyRes"}}}}}}}, "/gov/customs-duty/tax/inquiry/1.0": {"post": {"tags": ["governmentService"], "summary": "Inquiry customs duty", "description": "Inquiry customs duty", "operationId": "inquiry", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InquiryCustomsDutyReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListInquiryCustomsDutyRes"}}}}}}}}, "components": {"schemas": {"DataListTreasuryRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TreasuryRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResponseError": {"type": "object", "properties": {"errorCode": {"type": "string"}, "errorDesc": {"type": "string"}, "refVal": {"type": "object"}}, "description": "Addition Error List"}, "ResultListTreasuryRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTreasuryRes"}}}, "TreasuryRes": {"type": "object", "properties": {"treasuryCode": {"type": "string"}, "treasuryName": {"type": "string"}}, "description": "List data"}, "DataListTaxTypeRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TaxTypeRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTaxTypeRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTaxTypeRes"}}}, "TaxTypeRes": {"type": "object", "properties": {"taxTypeCode": {"type": "string"}, "taxTypeName": {"type": "string"}}, "description": "List data"}, "RevenueAuthorityReq": {"required": ["treasuryCode"], "type": "object", "properties": {"treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "1234567"}}}, "DataListRevenueAuthorityRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/RevenueAuthorityRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListRevenueAuthorityRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListRevenueAuthorityRes"}}}, "RevenueAuthorityRes": {"type": "object", "properties": {"revAuthCode": {"type": "string"}, "revAuthName": {"type": "string"}}, "description": "List data"}, "DataListRevenueAccountRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/RevenueAccountRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListRevenueAccountRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListRevenueAccountRes"}}}, "RevenueAccountRes": {"type": "object", "properties": {"revAccCode": {"type": "string"}, "revAccName": {"type": "string"}}, "description": "List data"}, "DataListExportImportType": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/ExportImportType"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ExportImportType": {"type": "object", "properties": {"eiTypeCode": {"type": "string"}, "eiTypeName": {"type": "string"}}, "description": "List data"}, "ResultListExportImportType": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListExportImportType"}}}, "DataListEconomicContentRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/EconomicContentRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "EconomicContentRes": {"type": "object", "properties": {"ecCode": {"type": "string"}, "ecName": {"type": "string"}}, "description": "List data"}, "ResultListEconomicContentRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListEconomicContentRes"}}}, "CustomsCurrencyRes": {"type": "object", "properties": {"ccCode": {"type": "string"}, "ccName": {"type": "string"}}, "description": "List data"}, "DataListCustomsCurrencyRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/CustomsCurrencyRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListCustomsCurrencyRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListCustomsCurrencyRes"}}}, "ChapterRes": {"type": "object", "properties": {"chapterCode": {"type": "string"}, "chapterName": {"type": "string"}}, "description": "List data"}, "DataListChapterRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/ChapterRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListChapterRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListChapterRes"}}}, "AdministrativeAreaReq": {"type": "object", "properties": {"treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "1234567"}}}, "AdministrativeAreaRes": {"type": "object", "properties": {"admAreaCode": {"type": "string"}, "admAreaName": {"type": "string"}}, "description": "List data"}, "DataListAdministrativeAreaRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/AdministrativeAreaRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListAdministrativeAreaRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListAdministrativeAreaRes"}}}, "TxnSaveReq": {"required": ["transKey"], "type": "object", "properties": {"transKey": {"maxLength": 150, "minLength": 0, "type": "string", "description": "Key giao d<PERSON>ch", "example": "TXNKEY_REQUEST_APPROVAL_297system_180208d2-4109-4ce3-85ca-b686abd84f86"}}}, "ResultString": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"type": "string", "description": "Data"}}}, "Filter": {"type": "object", "properties": {"operator": {"type": "string", "description": "ct/ eq/ neq/ gt/ gte/ lt/ lte"}, "field": {"type": "string"}, "value": {"type": "string"}}}, "Order": {"type": "object", "properties": {"field": {"type": "string"}, "direction": {"type": "string"}}}, "Page": {"type": "object", "properties": {"pageSize": {"minimum": 1, "type": "integer", "description": "Row number/ page, min = 1", "format": "int32"}, "pageNum": {"minimum": 1, "type": "integer", "description": "Page index (start from 1), min = 1", "format": "int32"}, "getTotal": {"type": "boolean", "description": "Get total record size flag"}}}, "TxnPendingListReq": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}}, "page": {"$ref": "#/components/schemas/Page"}, "search": {"maxLength": 70, "minLength": 0, "type": "string", "description": "Giá trị tìm kiếm theo: Số tờ khai || Mã giao dịch || Số tiền || Số tài khoản trích nợ", "example": "Nop thue"}, "startDate": {"type": "string", "description": "<PERSON><PERSON> ngày", "example": "2024-11-26"}, "endDate": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "2024-11-30"}, "minAmount": {"type": "string", "description": "<PERSON><PERSON> tiền tối thiểu", "example": 100000}, "maxAmount": {"type": "string", "description": "<PERSON><PERSON> tiền tối đa", "example": 1000000}, "ccy": {"type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ", "example": "VND"}, "statuses": {"type": "array", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "example": "['INIT']", "items": {"type": "string", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "example": "['INIT']"}}, "debitAccNo": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON>n trích nợ", "example": "1200046752"}, "taxCode": {"maxLength": 13, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> số thuế", "example": "9581856"}, "declarationNo": {"maxLength": 30, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> tờ khai hải quan", "example": "8481929515"}, "batchNo": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> tham chi<PERSON>u lô", "example": "1234567890"}}}, "DataListTxnPendingListRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TxnPendingListRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTxnPendingListRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTxnPendingListRes"}}}, "TxnPendingListRes": {"type": "object", "properties": {"txnId": {"type": "string"}, "debitAccNo": {"type": "string"}, "taxCode": {"type": "string"}, "declarationNo": {"type": "string"}, "amount": {"type": "string"}, "ccy": {"type": "string"}, "batchNo": {"type": "string"}, "status": {"type": "string"}, "createdDate": {"type": "string"}, "treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "0022"}, "treasuryName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kho bạc", "example": "<PERSON><PERSON> b<PERSON>c Nhà nước Hà Nội"}, "admAreaCode": {"maxLength": 5, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> đ<PERSON>a bàn hành <PERSON>h", "example": "009HH"}, "admAreaName": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> bàn hành ch<PERSON>h", "example": "<PERSON><PERSON>"}, "revAccCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON>ã tài k<PERSON>n thu", "example": "1898"}, "revAccName": {"type": "string", "description": "<PERSON><PERSON><PERSON> tài k<PERSON>n thu", "example": "<PERSON><PERSON><PERSON> thu 1898"}, "revAuthCode": {"maxLength": 7, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> cơ quan thu", "example": "1420"}, "revAuthName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> quan thu", "example": "<PERSON><PERSON> quan thu 1420"}, "bbCode": {"type": "string"}, "statusName": {"type": "string"}}, "description": "List data"}, "TxnListReq": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}}, "page": {"$ref": "#/components/schemas/Page"}, "search": {"maxLength": 70, "minLength": 0, "type": "string", "description": "Giá trị tìm kiếm theo: Số tờ khai || Mã giao dịch || Số tiền || Số tài khoản trích nợ", "example": "Nop thue"}, "startDate": {"type": "string", "description": "<PERSON><PERSON> ngày", "example": "2024-11-26"}, "endDate": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "2024-11-30"}, "minAmount": {"type": "string", "description": "<PERSON><PERSON> tiền tối thiểu", "example": 100000}, "maxAmount": {"type": "string", "description": "<PERSON><PERSON> tiền tối đa", "example": 1000000}, "ccy": {"type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ", "example": "VND"}, "statuses": {"type": "array", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "example": "['INIT']", "items": {"type": "string", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "example": "['INIT']"}}, "debitAccNo": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON>n trích nợ", "example": "1200046752"}, "taxCode": {"maxLength": 13, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> số thuế", "example": "9581856"}, "declarationNo": {"maxLength": 30, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> tờ khai hải quan", "example": "8481929515"}, "batchNo": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> tham chi<PERSON>u lô", "example": "1234567890"}}}, "DataListTxnListRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TxnListRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTxnListRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTxnListRes"}}}, "TxnListRes": {"type": "object", "properties": {"txnId": {"type": "string"}, "debitAccNo": {"type": "string"}, "taxCode": {"type": "string"}, "declarationNo": {"type": "string"}, "amount": {"type": "string"}, "ccy": {"type": "string"}, "batchNo": {"type": "string"}, "status": {"type": "string"}, "createdDate": {"type": "string"}, "treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "0022"}, "treasuryName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kho bạc", "example": "<PERSON><PERSON> b<PERSON>c Nhà nước Hà Nội"}, "admAreaCode": {"maxLength": 5, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> đ<PERSON>a bàn hành <PERSON>h", "example": "009HH"}, "admAreaName": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> bàn hành ch<PERSON>h", "example": "<PERSON><PERSON>"}, "revAccCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON>ã tài k<PERSON>n thu", "example": "1898"}, "revAccName": {"type": "string", "description": "<PERSON><PERSON><PERSON> tài k<PERSON>n thu", "example": "<PERSON><PERSON><PERSON> thu 1898"}, "revAuthCode": {"maxLength": 7, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> cơ quan thu", "example": "1420"}, "revAuthName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> quan thu", "example": "<PERSON><PERSON> quan thu 1420"}, "bbCode": {"type": "string"}, "approvalUsers": {"type": "string"}, "createdBy": {"type": "string"}, "statusName": {"type": "string"}}, "description": "List data"}, "TxnDetailReq": {"required": ["txnId"], "type": "object", "properties": {"txnId": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Mã giao d<PERSON>ch", "example": "DVC01704202411252339"}, "checkPerm": {"type": "boolean", "description": "<PERSON><PERSON><PERSON> tra ng<PERSON><PERSON><PERSON> du<PERSON> tiếp theo", "example": true}}}, "ResultTxnDetailRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TxnDetailRes"}}}, "TxnDetailRes": {"type": "object", "properties": {"accountingStatus": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> thái hạch toán", "example": "SUCCESS"}, "accountingStatusName": {"type": "string", "description": "<PERSON><PERSON>n trạng thái hạch toán", "example": "SUCCESS"}, "admAreaCode": {"maxLength": 5, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> đ<PERSON>a bàn hành <PERSON>h", "example": "009HH"}, "admAreaName": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> bàn hành ch<PERSON>h", "example": "<PERSON><PERSON>"}, "altPayerAddr": {"maxLength": 255, "minLength": 0, "type": "string", "description": "Đ<PERSON>a chỉ người nộp thay", "example": "<PERSON><PERSON>"}, "altPayerName": {"maxLength": 70, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> thay", "example": "<PERSON><PERSON><PERSON>"}, "altTaxCode": {"maxLength": 13, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> số thuế người nộp thay", "example": "**********"}, "amount": {"type": "string", "description": "<PERSON><PERSON> tiền", "example": *********}, "amountText": {"type": "string", "description": "<PERSON><PERSON> tiền bằng chữ", "example": "Một tỷ hai trăm ba mươi tư triệu năm trăm sáu mươi bảy nghìn tám trăm chín mươi"}, "approvalNote": {"type": "string", "description": "<PERSON>ý do từ chối", "example": "<PERSON>ý do từ chối"}, "approvedBy": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "example": "88060ktv"}, "batchNo": {"type": "string", "description": "Mã lô", "example": "BATCH001"}, "bbCode": {"type": "string"}, "ccy": {"type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ", "example": "VND"}, "createdBy": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> tạo", "example": "88060ktv"}, "createdDate": {"type": "string", "description": "<PERSON><PERSON><PERSON>"}, "customsConnStatus": {"type": "string", "description": "<PERSON>r<PERSON><PERSON> thái kết nối hải quan", "example": "<PERSON><PERSON><PERSON><PERSON> công"}, "customsConnStatusName": {"type": "string", "description": "<PERSON>ên trạng thái kết nối hải quan", "example": "<PERSON><PERSON><PERSON><PERSON> công"}, "debitAccName": {"type": "string", "description": " <PERSON>ên tài k<PERSON>n trích nợ", "example": "Công ty TNHH A"}, "debitAccNo": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON>n trích nợ", "example": "1200046752"}, "feeOpt": {"type": "string", "description": "<PERSON><PERSON><PERSON> thức thu phí", "example": "<PERSON><PERSON>"}, "feeTotal": {"type": "string", "description": "<PERSON><PERSON> (bao gồm VAT)", "example": 10000}, "payerAddr": {"maxLength": 255, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON> chỉ người nộp thuế", "example": "<PERSON><PERSON>"}, "payerName": {"maxLength": 70, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON> thuế", "example": "<PERSON><PERSON><PERSON>"}, "payerType": {"type": "integer", "description": " <PERSON><PERSON><PERSON> h<PERSON>nh ng<PERSON><PERSON><PERSON> nộp thuế: 0 - <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, 1 - <PERSON><PERSON><PERSON> <PERSON><PERSON>, 2 - c<PERSON> nhân", "format": "int32", "example": 1}, "payerTypeName": {"type": "string"}, "raNote": {"type": "string", "description": "<PERSON><PERSON> chú tới ng<PERSON><PERSON>", "example": "<PERSON><PERSON> chú tới ng<PERSON><PERSON>"}, "revAccCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON>ã tài k<PERSON>n thu", "example": "1898"}, "revAccName": {"type": "string", "description": "<PERSON><PERSON><PERSON> tài k<PERSON>n thu", "example": "<PERSON><PERSON><PERSON> thu 1898"}, "revAuthCode": {"maxLength": 7, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> cơ quan thu", "example": "1420"}, "revAuthName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> quan thu", "example": "<PERSON><PERSON> quan thu 1420"}, "status": {"type": "string", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "example": "SUCCESS"}, "statusName": {"type": "string", "description": "<PERSON>ên trạng thái", "example": "<PERSON><PERSON><PERSON><PERSON> công"}, "taxCode": {"maxLength": 13, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> số thuế người nộp", "example": "**********"}, "taxItems": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch thông tin k<PERSON>n nộp", "items": {"$ref": "#/components/schemas/TxnTaxFullItemDto"}}, "tccRefNo": {"type": "string", "description": "Số ref TCC", "example": "88060ktv"}, "treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "0022"}, "treasuryName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kho bạc", "example": "<PERSON><PERSON> b<PERSON>c Nhà nước Hà Nội"}, "txnId": {"type": "string", "description": "Mã giao d<PERSON>ch", "example": "TXN001"}}, "description": "Data"}, "TxnTaxFullItemDto": {"required": ["amount", "ccCode", "ccy", "chapterCode", "declarationDate", "declarationNo", "ecCode", "eiTypeCode", "payerType", "taxTypeCode", "transDesc"], "type": "object", "properties": {"eiTypeCode": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON>ã loại hình xuất nhập khẩu", "example": "A11"}, "taxTypeCode": {"maxLength": 10, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> sắc thuế", "example": "VA"}, "ccCode": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON>ã loại tiền hải quan", "example": "1"}, "chapterCode": {"maxLength": 3, "minLength": 0, "type": "string", "description": "Mã ch<PERSON>", "example": "755"}, "ecCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> nội dung kinh tế", "example": "3063"}, "amount": {"maxLength": 19, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> tiền", "example": "100000"}, "ccy": {"maxLength": 3, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ của số tiền nhập", "example": "VND"}, "declarationDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> tờ khai", "example": "2021-01-01"}, "declarationNo": {"maxLength": 30, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> tờ khai", "example": "1234567890"}, "transDesc": {"maxLength": 1000, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> giao d<PERSON>ch", "example": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> giao d<PERSON>ch"}, "ccName": {"type": "string", "description": "<PERSON><PERSON>n lo<PERSON>i tiền hải quan", "example": "<PERSON><PERSON><PERSON> xu<PERSON>t nh<PERSON> kh<PERSON>u"}, "chapterName": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "<PERSON><PERSON> tế tư nhân"}, "ecName": {"type": "string", "description": "<PERSON><PERSON><PERSON> n<PERSON>i dung kinh tế", "example": "<PERSON><PERSON> phí cấp gi<PERSON>y phép quy ho<PERSON>ch"}, "eiTypeName": {"type": "string", "description": "<PERSON><PERSON><PERSON> lo<PERSON>i hình xuất nhập kh<PERSON>u", "example": "<PERSON><PERSON><PERSON><PERSON> kinh doanh tiêu dùng"}, "taxTypeName": {"type": "string", "description": "<PERSON><PERSON><PERSON> thuế", "example": "<PERSON><PERSON><PERSON> giá trị gia tăng"}, "treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "0022"}, "treasuryName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kho bạc", "example": "<PERSON><PERSON> b<PERSON>c Nhà nước Hà Nội"}, "admAreaCode": {"maxLength": 5, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> đ<PERSON>a bàn hành <PERSON>h", "example": "009HH"}, "admAreaName": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> bàn hành ch<PERSON>h", "example": "<PERSON><PERSON>"}, "revAccCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON>ã tài k<PERSON>n thu", "example": "1898"}, "revAccName": {"type": "string", "description": "<PERSON><PERSON><PERSON> tài k<PERSON>n thu", "example": "<PERSON><PERSON><PERSON> thu 1898"}, "revAuthCode": {"maxLength": 7, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> cơ quan thu", "example": "1420"}, "revAuthName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> quan thu", "example": "<PERSON><PERSON> quan thu 1420"}, "payerType": {"type": "integer", "description": "<PERSON><PERSON><PERSON> h<PERSON>nh ng<PERSON><PERSON><PERSON> nộp thuế: 0 - <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, 1 - <PERSON><PERSON><PERSON> <PERSON><PERSON>, 2 - c<PERSON> nhân", "format": "int32", "example": 1}, "payerTypeName": {"type": "string"}}, "description": "<PERSON><PERSON> s<PERSON>ch thông tin k<PERSON>n nộp"}, "TxnDeleteReq": {"required": ["txnIds"], "type": "object", "properties": {"txnIds": {"type": "array", "description": "<PERSON><PERSON>c mã giao d<PERSON>ch", "example": ["12345", "67890"], "items": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON><PERSON>c mã giao d<PERSON>ch", "example": "[\"12345\",\"67890\"]"}}}}, "ResultTxnDeleteRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TxnDeleteRes"}}}, "TxnDeleteDto": {"type": "object", "properties": {"code": {"type": "string"}, "txnId": {"type": "string"}, "message": {"type": "string"}}}, "TxnDeleteRes": {"type": "object", "properties": {"successTxns": {"type": "array", "items": {"$ref": "#/components/schemas/TxnDeleteDto"}}, "failTxns": {"type": "array", "items": {"$ref": "#/components/schemas/TxnDeleteDto"}}, "totalSuccess": {"type": "integer", "format": "int64"}, "totalFail": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}}, "description": "Data"}, "TemplateSaveReq": {"required": ["templateName", "txnId"], "type": "object", "properties": {"txnId": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Mã giao d<PERSON>ch", "example": "DVC01704202411252339"}, "templateName": {"maxLength": 100, "minLength": 0, "type": "string", "description": "Tên template", "example": "Mẫu n<PERSON><PERSON> thuế 1"}, "isPublic": {"type": "boolean", "description": "<PERSON><PERSON><PERSON> mẫu công khai", "example": true}}}, "TxnTemplateListReq": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}}, "page": {"$ref": "#/components/schemas/Page"}, "search": {"maxLength": 70, "minLength": 0, "type": "string", "description": "Giá trị tìm kiếm theo: Số tờ khai || Mã giao dịch || Số tiền || Số tài khoản trích nợ", "example": "Nop thue"}}}, "DataListTxnTemplateListRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TxnTemplateListRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTxnTemplateListRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTxnTemplateListRes"}}}, "TxnTemplateListRes": {"type": "object", "properties": {"templateName": {"type": "string"}, "revAuthCode": {"type": "string"}, "revAuthName": {"type": "string"}}, "description": "List data"}, "TxnTaxItemDto": {"required": ["amount", "ccCode", "ccy", "chapterCode", "declarationDate", "declarationNo", "ecCode", "eiTypeCode", "taxTypeCode", "transDesc"], "type": "object", "properties": {"eiTypeCode": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON>ã loại hình xuất nhập khẩu", "example": "A11"}, "taxTypeCode": {"maxLength": 10, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> sắc thuế", "example": "VA"}, "ccCode": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON>ã loại tiền hải quan", "example": "1"}, "chapterCode": {"maxLength": 3, "minLength": 0, "type": "string", "description": "Mã ch<PERSON>", "example": "755"}, "ecCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> nội dung kinh tế", "example": "3063"}, "amount": {"maxLength": 19, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> tiền", "example": "100000"}, "ccy": {"maxLength": 3, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ của số tiền nhập", "example": "VND"}, "declarationDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> tờ khai", "example": "2021-01-01"}, "declarationNo": {"maxLength": 30, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> tờ khai", "example": "1234567890"}, "transDesc": {"maxLength": 1000, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> giao d<PERSON>ch", "example": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> giao d<PERSON>ch"}}, "description": "<PERSON><PERSON> s<PERSON>ch thông tin k<PERSON>n nộp"}, "ValidateCustomsDutyReq": {"required": ["admAreaCode", "amount", "ccy", "debitAccNo", "payerAddr", "payerName", "payerType", "revAccCode", "revAuthCode", "taxCode", "taxItems", "treasuryCode", "txnType"], "type": "object", "properties": {"amount": {"maxLength": 19, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> tiền", "example": "100000"}, "ccy": {"maxLength": 3, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ của số tiền nhập", "example": "VND"}, "treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "0022"}, "admAreaCode": {"maxLength": 5, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> đ<PERSON>a bàn hành <PERSON>h", "example": "009HH"}, "revAuthCode": {"maxLength": 7, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> cơ quan thu", "example": "1420"}, "revAccCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON>ã tài k<PERSON>n thu", "example": "1898"}, "debitAccNo": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON>n trích nợ", "example": "1200046752"}, "taxCode": {"maxLength": 13, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> số thuế người nộp", "example": "**********"}, "payerName": {"maxLength": 70, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON> thuế", "example": "<PERSON><PERSON><PERSON>"}, "payerAddr": {"maxLength": 255, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON> chỉ người nộp thuế", "example": "<PERSON><PERSON>"}, "altTaxCode": {"maxLength": 13, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> số thuế người nộp thay", "example": "**********"}, "altPayerName": {"maxLength": 70, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> thay", "example": "<PERSON><PERSON><PERSON>"}, "altPayerAddr": {"maxLength": 255, "minLength": 0, "type": "string", "description": "Đ<PERSON>a chỉ người nộp thay", "example": "<PERSON><PERSON>"}, "payerType": {"type": "integer", "description": "<PERSON><PERSON><PERSON> h<PERSON>nh ng<PERSON><PERSON><PERSON> nộp thuế: 0 - <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, 1 - <PERSON><PERSON><PERSON> <PERSON><PERSON>, 2 - c<PERSON> nhân", "format": "int32", "example": 1}, "raNote": {"maxLength": 210, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> chú tới ng<PERSON><PERSON>", "example": "<PERSON><PERSON><PERSON> thu<PERSON> do<PERSON>h nghi<PERSON><PERSON> hàng tháng"}, "txnType": {"maxLength": 1, "minLength": 0, "type": "string", "description": "Mã loại thuế: 1 là thuế nội địa. 3 là phí hạ tầng cảng biển. 4 là thuế hải quan", "example": "4"}, "taxItems": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch thông tin k<PERSON>n nộp", "items": {"$ref": "#/components/schemas/TxnTaxItemDto"}}}}, "ResultValidateCustomsDutyRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/ValidateCustomsDutyRes"}}}, "ValidateCustomsDutyRes": {"type": "object", "properties": {"transKey": {"type": "string", "description": "Key giao d<PERSON>ch", "example": "TXNKEY_REQUEST_APPROVAL_297system_180208d2-4109-4ce3-85ca-b686abd84f86"}, "feeTotal": {"type": "string", "description": "<PERSON><PERSON> (bao gồm VAT)", "example": 10000}, "feeCcy": {"type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ", "example": "VND"}, "feeOpt": {"type": "string", "description": "<PERSON><PERSON><PERSON> thức thu phí", "example": "<PERSON><PERSON>"}, "amount": {"type": "string", "description": "<PERSON><PERSON> tiền", "example": *********}, "ccy": {"type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ", "example": "VND"}, "amountText": {"type": "string", "description": "<PERSON><PERSON> tiền bằng chữ", "example": "Một tỷ hai trăm ba mươi tư triệu năm trăm sáu mươi bảy nghìn tám trăm chín mươi"}}, "description": "Data"}, "InquiryCustomsDutyReq": {"required": ["taxCode"], "type": "object", "properties": {"taxCode": {"maxLength": 13, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> số thuế người nộp", "example": "**********"}, "declarationNo": {"maxLength": 30, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> tờ khai <PERSON> quan", "example": "43233242423"}, "declarationYear": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON> tờ khai", "example": "2025"}}}, "DataListInquiryCustomsDutyRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/InquiryCustomsDutyRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "InquiryCustomsDutyRes": {"required": ["amount", "ccCode", "ccy", "chapterCode", "declarationDate", "declarationNo", "ecCode", "eiTypeCode", "payerType", "taxTypeCode", "transDesc"], "type": "object", "properties": {"eiTypeCode": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON>ã loại hình xuất nhập khẩu", "example": "A11"}, "taxTypeCode": {"maxLength": 10, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> sắc thuế", "example": "VA"}, "ccCode": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON>ã loại tiền hải quan", "example": "1"}, "chapterCode": {"maxLength": 3, "minLength": 0, "type": "string", "description": "Mã ch<PERSON>", "example": "755"}, "ecCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> nội dung kinh tế", "example": "3063"}, "amount": {"maxLength": 19, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> tiền", "example": "100000"}, "ccy": {"maxLength": 3, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ của số tiền nhập", "example": "VND"}, "declarationDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> tờ khai", "example": "2021-01-01"}, "declarationNo": {"maxLength": 30, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> tờ khai", "example": "1234567890"}, "transDesc": {"maxLength": 1000, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> giao d<PERSON>ch", "example": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> giao d<PERSON>ch"}, "ccName": {"type": "string", "description": "<PERSON><PERSON>n lo<PERSON>i tiền hải quan", "example": "<PERSON><PERSON><PERSON> xu<PERSON>t nh<PERSON> kh<PERSON>u"}, "chapterName": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "<PERSON><PERSON> tế tư nhân"}, "ecName": {"type": "string", "description": "<PERSON><PERSON><PERSON> n<PERSON>i dung kinh tế", "example": "<PERSON><PERSON> phí cấp gi<PERSON>y phép quy ho<PERSON>ch"}, "eiTypeName": {"type": "string", "description": "<PERSON><PERSON><PERSON> lo<PERSON>i hình xuất nhập kh<PERSON>u", "example": "<PERSON><PERSON><PERSON><PERSON> kinh doanh tiêu dùng"}, "taxTypeName": {"type": "string", "description": "<PERSON><PERSON><PERSON> thuế", "example": "<PERSON><PERSON><PERSON> giá trị gia tăng"}, "treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "0022"}, "treasuryName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kho bạc", "example": "<PERSON><PERSON> b<PERSON>c Nhà nước Hà Nội"}, "admAreaCode": {"maxLength": 5, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> đ<PERSON>a bàn hành <PERSON>h", "example": "009HH"}, "admAreaName": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> bàn hành ch<PERSON>h", "example": "<PERSON><PERSON>"}, "revAccCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON>ã tài k<PERSON>n thu", "example": "1898"}, "revAccName": {"type": "string", "description": "<PERSON><PERSON><PERSON> tài k<PERSON>n thu", "example": "<PERSON><PERSON><PERSON> thu 1898"}, "revAuthCode": {"maxLength": 7, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> cơ quan thu", "example": "1420"}, "revAuthName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> quan thu", "example": "<PERSON><PERSON> quan thu 1420"}, "payerType": {"type": "integer", "description": "<PERSON><PERSON><PERSON> h<PERSON>nh ng<PERSON><PERSON><PERSON> nộp thuế: 0 - <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, 1 - <PERSON><PERSON><PERSON> <PERSON><PERSON>, 2 - c<PERSON> nhân", "format": "int32", "example": 1}, "payerTypeName": {"type": "string"}}, "description": "Th<PERSON>ng tin tra cứu thuế hải quan"}, "ResultListInquiryCustomsDutyRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListInquiryCustomsDutyRes"}}}}}, "tags": ["governmentService"], "formated": "1"}