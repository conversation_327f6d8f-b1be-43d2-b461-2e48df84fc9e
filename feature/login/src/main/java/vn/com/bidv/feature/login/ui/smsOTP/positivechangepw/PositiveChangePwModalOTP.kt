package vn.com.bidv.feature.login.ui.smsOTP.positivechangepw

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.ui.BaseDialogScreen
import vn.com.bidv.feature.login.domain.VerifyPwOtpErrorType
import vn.com.bidv.feature.login.domain.model.ModelSmsOTPConfigDMO
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPContent
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPReducer
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.localization.R as RLocalization
import vn.com.bidv.sdkbase.navigation.NavigationHelper as SdkBaseNavigationHelper

@Composable
fun PositiveChangePwModalOTP(
    navController: NavHostController,
    modelConfig: ModelSmsOTPConfigDMO,
) {
    val viewModel: PositiveChangePwModalOTPViewModel = hiltViewModel()
    var isShowErrorPopup by remember { mutableStateOf(false) }
    var isShowErrorMessage by remember { mutableStateOf("") }
    var isBackToSetting by remember { mutableStateOf(false) }
    val messageSuccess = remember { mutableStateOf("") }
    BaseDialogScreen(
        viewModel = viewModel,
        renderContent = { uiState, onEvent ->
            if (!uiState.initSuccess) {
                onEvent(
                    BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.InitData(
                        modelConfig
                    )
                )
            }
            BaseIBankModalOTPContent(
                uiState = uiState,
                onEvent = onEvent,
                viewModel = viewModel
            )
            if (isShowErrorPopup) {
                IBankModalConfirm(
                    title = stringResource(RLocalization.string.loi),
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = isShowErrorMessage,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(RLocalization.string.close),
                        )
                    ),
                    onDismissRequest = {
                        isShowErrorPopup = false
                        if (isBackToSetting) {
                            navController.popBackStack(
                                IBankMainRouting.AuthRoutes.PositiveChangePwRoute.route,
                                inclusive = true
                            )
                        } else {
                            onEvent(BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.ClearOTP)
                        }
                    }
                )
            }

            if (messageSuccess.value.isNotEmpty()) {
                IBankModalConfirm(
                    title = stringResource(R.string.doi_mat_khau_thanh_cong),
                    modalConfirmType = ModalConfirmType.Success,
                    supportingText = messageSuccess.value,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.dang_nhap_lai),
                        )
                    ),
                    onDismissRequest = {
                        messageSuccess.value = ""
                        SdkBaseNavigationHelper.navigationToLogin(navController)
                    }
                )
            }
        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPError -> {
                    isShowErrorPopup = true
                    isShowErrorMessage =
                        (sideEffect.errorMessage ?: navController.context.getString(
                            RLocalization.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai
                        ))
                    isBackToSetting = sideEffect.errorCode == VerifyPwOtpErrorType.SMS_OTP_BLOCK
                }

                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPSuccess<Boolean> -> {
                    messageSuccess.value =
                        navController.context.getString(R.string.quy_khach_vui_long_dang_nhap_lai_de_tiep_tuc_su_dung_dich_vu)
                }

                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.OnDismiss -> {
                    navController.popBackStack()
                }

                else -> {
                    // no thing
                }
            }

        }
    )
}