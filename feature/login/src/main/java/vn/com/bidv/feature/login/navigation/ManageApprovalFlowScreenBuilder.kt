package vn.com.bidv.feature.login.navigation

import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionFlowScreenBuilder
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant
import vn.com.bidv.feature.login.domain.ManageApprovalRequestsVerifyUseCase
import vn.com.bidv.feature.login.ui.approvalrequestsresult.ApprovalRequestsResultScreen
import javax.inject.Inject

class ManageApprovalFlowScreenBuilder @Inject constructor(
    useCase: ManageApprovalRequestsVerifyUseCase,
) : VerifyTransactionFlowScreenBuilder(
    useCase = useCase,
    type = VerifyTransactionTypeConstant.ManageApprovalRequests,
    content = { navController, jsonData, _ ->
        ApprovalRequestsResultScreen(navController, jsonData)
    }
)