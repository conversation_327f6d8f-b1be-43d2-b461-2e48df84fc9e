package vn.com.bidv.feature.login.ui.smsOTP.forgotPwOtp

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.fromHtml
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseDialogScreen
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.constants.LoginErrorCode
import vn.com.bidv.feature.login.domain.model.ModelForgotPwOTPConfigDMO
import vn.com.bidv.feature.login.domain.model.ModelLoginDMO
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPContent
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPReducer
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.navigation.NavigationHelper as SdkBaseNavigationHelper

@Composable
fun ForgotPwModalOTPScreen(
    navController: NavHostController,
    modelConfig: ModelForgotPwOTPConfigDMO,
) {
    val viewModel: ForgotPwModalOTPViewModel = hiltViewModel()
    var isShowErrorPopup by rememberSaveable { mutableStateOf(false) }
    var isShowErrorMessage by rememberSaveable { mutableStateOf("") }
    var isShowSuccessPopup by rememberSaveable { mutableStateOf(false) }
    var isNavigateToLogin by remember { mutableStateOf(false) }

    val isAdmin = modelConfig.userRole?.contains(
        Constants.ADMIN_ROLE,
        ignoreCase = true
    )

    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current

    BaseDialogScreen(
        viewModel = viewModel,
        renderContent = { uiState, onEvent ->
            if (!uiState.initSuccess) {
                onEvent(
                    BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.InitData(
                        modelConfig
                    )
                )
            }
            BaseIBankModalOTPContent(
                uiState = uiState,
                onEvent = onEvent,
                viewModel = viewModel
            )
            if (isShowErrorPopup) {
                IBankModalConfirm(
                    title = stringResource(R.string.loi),
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = isShowErrorMessage,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.close),
                        )
                    ),
                    onDismissRequest = {
                        isShowErrorPopup = false
                        if (isNavigateToLogin) {
                            SdkBaseNavigationHelper.navigationToLogin(navController)
                        } else {
                            onEvent(BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.ClearOTP)
                        }
                    }
                )
            }

            if (isShowSuccessPopup) {

                val adminAnnotatedString = AnnotatedString.fromHtml(stringResource(R.string.cdatamat_khau_moi_se_duoc_gui_den_email_cua_quy_khach_vui_long_bkiem_tra_email_dang_nhap_va_doi_mat_khaub_de_tiep_tuc_su_dung_dich_vu))
                val notAdminAnnotatedString = AnnotatedString.fromHtml(stringResource(R.string.cdatayeu_cau_quen_mat_khau_da_duoc_bgui_den_nguoi_dung_co_vai_tro_quan_tri_vien_de_phe_duyetbsau_khi_duoc_phe_duyet_quy_khach_vui_long_bkiem_tra_emailb_de_nhan_thong_tin_mat_khau_moi))

                IBankModalConfirm(
                    isShowIconClose = false,
                    modalConfirmType = ModalConfirmType.Success,
                    title = if (isAdmin == true) stringResource(R.string.yeu_cau_thanh_cong)
                    else stringResource(R.string.day_duyet_thanh_cong),
                    supportingText = "",
                    annotatedSupportingText = if (isAdmin == true) adminAnnotatedString
                    else notAdminAnnotatedString,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.hoan_thanh),
                            onClick = {
                                SdkBaseNavigationHelper.navigationToLogin(navController)
                            }
                        )
                    ),
                    onDismissRequest = {
                        isShowSuccessPopup = false
                    }
                )
            }
        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPError -> {
                    isShowErrorPopup = true
                    isNavigateToLogin = sideEffect.errorCode == LoginErrorCode.IM9004
                    isShowErrorMessage =
                        (sideEffect.errorMessage ?: navController.context.getString(
                            R.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai
                        ))
                }

                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPSuccess<ModelLoginDMO> -> {
                    isShowSuccessPopup = true
                }

                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.OnDismiss -> {
                    navController.popBackStack()
                }

                else -> {

                }
            }

        }
    )
}