package vn.com.bidv.feature.login.navigation

import vn.com.bidv.feature.common.navigation.VerifyTransactionRoute
import vn.com.bidv.feature.login.constants.LoginNotificationRouteBuilderConstants
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.sdkbase.navigation.notifyroute.NotiParam
import vn.com.bidv.sdkbase.navigation.notifyroute.NotiRoute
import vn.com.bidv.sdkbase.navigation.notifyroute.NotificationRouteBuilder

class LoginNotificationRouteBuilder : NotificationRouteBuilder {
    override fun buildRoute(
        navigateId: String,
        defaultParamKeys: Set<String>?,
        listParam: List<NotiParam>?
    ): NotiRoute? {
        val loginNotificationRoute = LoginNotificationRoute.mapToLoginNotificationRoute(navigateId)
        return when (loginNotificationRoute) {
            LoginNotificationRoute.MANAGER_PENDING_ROUTE_BUILDER -> NotiRoute(
                route = IBankMainRouting.AuthRoutes.ManageApprovalRequestsRoute.route,
                listParam = emptyList()
            )

            LoginNotificationRoute.SCAN_QR_ROUTE_BUILDER -> NotiRoute(
                route = VerifyTransactionRoute.ScanQRScreenRoute.route,
                listParam = emptyList()
            )

            else -> null
        }
    }
}

enum class LoginNotificationRoute(
    val navigateId: String,
    val isNeedToLogin: Boolean
) {
    MANAGER_PENDING_ROUTE_BUILDER(
        navigateId = LoginNotificationRouteBuilderConstants.MANAGER_PENDING_ROUTE_BUILDER,
        isNeedToLogin = true
    ),
    SCAN_QR_ROUTE_BUILDER(
        navigateId = LoginNotificationRouteBuilderConstants.SCAN_QR_ROUTE_BUILDER,
        isNeedToLogin = false
    );

    companion object {
        fun mapToLoginNotificationRoute(
            navigateId: String
        ): LoginNotificationRoute? {
            return entries.find { it.navigateId.lowercase() == navigateId.lowercase() }
        }
    }
}