package vn.com.bidv.feature.login.ui.changepin

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.common.domain.data.AdditionalInfoDMO
import vn.com.bidv.feature.common.domain.data.TransAuthDMO
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.ui.changepin.RequestChangePinModalReducer.RequestChangePinModalViewEffect
import vn.com.bidv.feature.login.ui.changepin.RequestChangePinModalReducer.RequestChangePinModalViewEvent
import vn.com.bidv.feature.login.ui.changepin.RequestChangePinModalReducer.RequestChangePinModalViewState
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class RequestChangePinModalViewModel @Inject constructor(
    private val userUseCase: UserInfoUseCase,
) : ViewModelIBankBase<RequestChangePinModalViewState, RequestChangePinModalViewEvent, RequestChangePinModalViewEffect>(
    initialState = RequestChangePinModalViewState(),
    reducer = RequestChangePinModalReducer()
) {
    override fun handleEffect(
        sideEffect: RequestChangePinModalViewEffect,
        onResult: (RequestChangePinModalViewEvent) -> Unit
    ) {
        if (sideEffect is RequestChangePinModalViewEffect.InitEffect) {
            val user =
                userUseCase.getUserInfoFromStorage().getSafeData()?.user
            onResult(
                RequestChangePinModalViewEvent.InitSuccessEvent(
                    user = user,
                    transAuthDMO = TransAuthDMO(
                        isSameDevice = Constants.SAME_DEVICE,
                        userId = user?.userId?.toString(),
                        isGenOtp = false,
                        additionalInfo = AdditionalInfoDMO(
                            totalTrans = 1,
                        )
                    )
                )
            )
        }
    }
}
