package vn.com.bidv.feature.login.domain.model

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable
import vn.com.bidv.feature.common.domain.data.SmartOtpDMO

@Serializable
data class ModelLoginDMO(
    @SerializedName("status")
    val status: String? = null,
    @SerializedName("langCode")
    val langCode: String? = null,
    @SerializedName("userFullName")
    val userFullName: String? = null,
    @SerializedName("forceChangePw")
    val forceChangePw: Boolean? = null,
    @SerializedName("cifResidencyStatus")
    val cifResidencyStatus: String? = null,
    @SerializedName("cifName")
    val cifName: String? = null,
    @SerializedName("userRole")
    val userRole: String? = null,
    @SerializedName("isSecurityQuestionSet")
    val isSecurityQuestionSet: Boolean? = null,
    @SerializedName("smartOtp")
    val smartOtpDMO: SmartOtpDMO? = null,
    @SerializedName("token")
    val token: Token? = null,
    @SerializedName("otp")
    val otp: ModelCreateOtpResDMO? = null,
    @SerializedName("userId")
    val userId: String? = null,
    @SerializedName("username")
    val username: String? = null,
    var isShowSecurityQuestionScreen: Boolean = false,
    var isShowSmartOtpScreen: Boolean = false,
)

@Serializable
data class Token(
    @SerializedName("access_token")
    val accessToken: String? = null,
    @SerializedName("expires_in")
    val expiresIn: Int? = null,
    @SerializedName("refresh_expires_in")
    val refreshExpiresIn: Int? = null,
    @SerializedName("refresh_token")
    val refreshToken: String? = null,
    @SerializedName("token_type")
    val tokenType: String? = null,
    @SerializedName("not-before-policy")
    val notBeforePolicy: Int? = null,
    @SerializedName("session_state")
    val sessionState: String? = null,
    @SerializedName("scope")
    val scope: String? = null
)