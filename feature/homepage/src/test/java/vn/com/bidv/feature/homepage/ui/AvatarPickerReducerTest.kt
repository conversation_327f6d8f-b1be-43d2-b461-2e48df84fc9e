package vn.com.bidv.feature.homepage.ui

import android.net.Uri
import io.mockk.mockk
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import vn.com.bidv.feature.homepage.ui.avatarpicker.AvatarPickerReducer

class AvatarPickerReducerTest {

    private lateinit var reducer: AvatarPickerReducer

    @Before
    fun setUp() {
        reducer = AvatarPickerReducer()
    }

    @Test
    fun `test OnInitData event`() {
        val initialState = AvatarPickerReducer.AvatarPickerViewState()
        val event = AvatarPickerReducer.AvatarPickerViewEvent.OnInitData

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState, newState)
        assertEquals(AvatarPickerReducer.AvatarPickerViewEffect.OnInitData, effect)
    }

    @Test
    fun `test OnInitDataSuccess event`() {
        val initialState = AvatarPickerReducer.AvatarPickerViewState()
        val profileImage = "http://example.com/image.jpg"
        val event = AvatarPickerReducer.AvatarPickerViewEvent.OnInitDataSuccess(profileImage)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(
            initialState.copy(isInitSuccess = true, profileImageUrl = profileImage),
            newState
        )
        assertEquals(null, effect)
    }

    @Test
    fun `test OnChangeAvatar event`() {
        val initialState = AvatarPickerReducer.AvatarPickerViewState()
        val imageUri = mockk<Uri>(relaxed = true)
        val event = AvatarPickerReducer.AvatarPickerViewEvent.OnChangeAvatar(imageUri)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState, newState)
        assertEquals(AvatarPickerReducer.AvatarPickerViewEffect.ChangeAvatar(imageUri), effect)
    }

    @Test
    fun `test OnChangeAvatarSuccess event`() {
        val initialState = AvatarPickerReducer.AvatarPickerViewState()
        val avatarUrl = "http://example.com/avatar.jpg"
        val event = AvatarPickerReducer.AvatarPickerViewEvent.OnChangeAvatarSuccess(avatarUrl)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(
            initialState.copy(profileImageUrl = avatarUrl),
            newState
        )
        assertEquals(null, effect)
    }
}