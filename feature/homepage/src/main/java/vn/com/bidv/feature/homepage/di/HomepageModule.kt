package vn.com.bidv.feature.homepage.di 
 
import dagger.Module 
import dagger.Provides 
import dagger.hilt.InstallIn 
import dagger.hilt.components.SingletonComponent 
import dagger.multibindings.IntoSet
import retrofit2.Retrofit
import vn.com.bidv.feature.common.data.utilities.apis.UtilitiesApi
import vn.com.bidv.feature.homepage.data.HomepageRepository
import vn.com.bidv.feature.homepage.data.homepage_account_balance.apis.HomepageAccountBalanceApi
import vn.com.bidv.feature.homepage.data.homepage_overview_widget.apis.HomepageOverviewWidgetApi
import vn.com.bidv.feature.homepage.navigation.HomepageNavigation
import vn.com.bidv.sdkbase.navigation.FeatureGraphBuilder 
import javax.inject.Singleton 
 
@Module 
@InstallIn(SingletonComponent::class) 
class HomepageModule { 
    @Provides 
    @Singleton 
    fun provideHomepageRepository(
        utilitiesApi: UtilitiesApi,
        homepageAccountBalanceApi: HomepageAccountBalanceApi,
    ): HomepageRepository {
        return HomepageRepository(
            utilitiesApi,
            homepageAccountBalanceApi,
        )
    }

    @Singleton
    @Provides
    fun provideHomepageOverviewWidgetApi(retrofit: Retrofit): HomepageOverviewWidgetApi {
        return retrofit.create(HomepageOverviewWidgetApi::class.java)
    }

    @Singleton 
    @Provides 
    @IntoSet 
    fun provideHomepageFeatureGraphBuilder(): FeatureGraphBuilder { 
        return HomepageNavigation() 
    }

    @Singleton
    @Provides
    fun provideHomepageAccountBalanceApi(retrofit: Retrofit): HomepageAccountBalanceApi =
        retrofit.create(HomepageAccountBalanceApi::class.java)
}
