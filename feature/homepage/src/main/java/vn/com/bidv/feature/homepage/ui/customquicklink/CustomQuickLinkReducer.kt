package vn.com.bidv.feature.homepage.ui.customquicklink

import androidx.compose.runtime.Immutable
import vn.com.bidv.feature.homepage.ui.customquicklink.draggable.rearrangeItems
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.feature.common.domain.data.PermissionResDMO
import vn.com.bidv.feature.homepage.constants.HomePageConstants

class CustomQuickLinkReducer :
    Reducer<CustomQuickLinkReducer.CustomQuickLinkViewState, CustomQuickLinkReducer.CustomQuickLinkViewEvent, CustomQuickLinkReducer.CustomQuickLinkViewEffect> {

    @Immutable
    data class CustomQuickLinkViewState(
        val isInitSuccess: Boolean = false,
        val textSearch: String = "",
        val isSearchView: Boolean = false,
        val listChildrenService: List<ModelCheckAble<PermissionResDMO>> = emptyList(),
        val listSearchChildrenService: List<ModelCheckAble<PermissionResDMO>> = emptyList(),
        val listQuickLink: List<PermissionResDMO> = emptyList()
    ) : ViewState


    @Immutable
    sealed class CustomQuickLinkViewEvent : ViewEvent {
        data object OnInitDataService : CustomQuickLinkViewEvent()
        data class UpdateSearchText(val searchText: String) : CustomQuickLinkViewEvent()
        data class SetSearchView(val isSearchView: Boolean): CustomQuickLinkViewEvent()
        data class GetServicesFromStorageSuccess(val listChildrenService: List<ModelCheckAble<PermissionResDMO>>?, val listQuickLink: List<PermissionResDMO>) : CustomQuickLinkViewEvent()
        data class GetSearchListChildrenService(val listSearchChildrenService: List<ModelCheckAble<PermissionResDMO>>?): CustomQuickLinkViewEvent()
        data class SelectItem(val item: ModelCheckAble<PermissionResDMO>) : CustomQuickLinkViewEvent()
        data class RearrangeItemsQuickLink(val fromIndex: Int, val toIndex: Int) : CustomQuickLinkViewEvent()
        data class HandleSelectItemResult(val listQuickLink: List<PermissionResDMO>, val listChildrenService: List<ModelCheckAble<PermissionResDMO>>?, val listSearchChildrenService: List<ModelCheckAble<PermissionResDMO>>?) : CustomQuickLinkViewEvent()
        data class UpdateUserQuickLink(val listQuickLink: List<PermissionResDMO>): CustomQuickLinkViewEvent()
        data object UpdateUserQuickLinkSuccessEvent : CustomQuickLinkViewEvent()
    }

    @Immutable
    sealed class CustomQuickLinkViewEffect : SideEffect {
        data object GetPermissionResFromStorage: CustomQuickLinkViewEffect()
        data class GetDataSearch(val listChildrenService: List<ModelCheckAble<PermissionResDMO>>?, val searchText: String) : CustomQuickLinkViewEffect()
        data class HandleSelectItem(val listQuickLink: List<PermissionResDMO>, val listChildrenService: List<ModelCheckAble<PermissionResDMO>>,val listSearchChildrenService: List<ModelCheckAble<PermissionResDMO>> ,val item: ModelCheckAble<PermissionResDMO>) : CustomQuickLinkViewEffect()
        data class UpdateUserQuickLinkAction(val listQuickLink: List<PermissionResDMO>): CustomQuickLinkViewEffect()
        data object UpdateUserQuickLinkSuccessEffect : CustomQuickLinkViewEffect(), UIEffect
    }

    override fun reduce(
        previousState: CustomQuickLinkViewState,
        event: CustomQuickLinkViewEvent,
    ): Pair<CustomQuickLinkViewState, CustomQuickLinkViewEffect?> {
         return when (event) {
             is CustomQuickLinkViewEvent.OnInitDataService -> {
                 previousState.copy(
                     isInitSuccess = true,
                 ) to CustomQuickLinkViewEffect.GetPermissionResFromStorage
             }

             is CustomQuickLinkViewEvent.GetServicesFromStorageSuccess -> {
                 previousState.copy(
                     listChildrenService = event.listChildrenService ?: emptyList(),
                     listSearchChildrenService = event.listChildrenService?.filter {  it.data.priorityLevel in HomePageConstants.validPriorityNormalQuickLink } ?: emptyList(),
                     listQuickLink = event.listQuickLink
                 ) to null
             }

             is CustomQuickLinkViewEvent.UpdateSearchText -> {
                 if (previousState.textSearch == event.searchText) {
                     previousState to null
                 } else {
                     previousState.copy(
                         textSearch = event.searchText
                     ) to CustomQuickLinkViewEffect.GetDataSearch(listChildrenService = previousState.listChildrenService, searchText = event.searchText)
                 }
             }

             is CustomQuickLinkViewEvent.SetSearchView -> {
                 if (previousState.isSearchView == event.isSearchView) {
                     previousState to null
                 } else {
                     previousState.copy(
                         isSearchView = event.isSearchView,
                         textSearch = "",
                         listSearchChildrenService = previousState.listChildrenService.filter {  it.data.priorityLevel in HomePageConstants.validPriorityNormalQuickLink }
                     ) to null
                 }
             }

             is CustomQuickLinkViewEvent.GetSearchListChildrenService -> {
                 previousState.copy(
                     listSearchChildrenService = event.listSearchChildrenService ?: emptyList()
                 ) to null
             }

             is CustomQuickLinkViewEvent.SelectItem -> {
                 previousState to CustomQuickLinkViewEffect.HandleSelectItem(
                     listQuickLink = previousState.listQuickLink,
                     listChildrenService = previousState.listChildrenService,
                     item = event.item,
                     listSearchChildrenService = previousState.listSearchChildrenService
                 )
             }

             is CustomQuickLinkViewEvent.RearrangeItemsQuickLink -> {
                 previousState.copy(
                     listQuickLink = previousState.listQuickLink.toMutableList().rearrangeItems(event.fromIndex, event.toIndex)
                 ) to null
             }

             is CustomQuickLinkViewEvent.HandleSelectItemResult -> {
                 previousState.copy(
                     listChildrenService = event.listChildrenService ?: emptyList(),
                     listQuickLink = event.listQuickLink,
                     listSearchChildrenService = event.listSearchChildrenService ?: emptyList()
                 ) to null
             }

             is CustomQuickLinkViewEvent.UpdateUserQuickLink -> {
                 previousState to CustomQuickLinkViewEffect.UpdateUserQuickLinkAction(
                     event.listQuickLink
                 )
             }

             is CustomQuickLinkViewEvent.UpdateUserQuickLinkSuccessEvent -> {
                 previousState to CustomQuickLinkViewEffect.UpdateUserQuickLinkSuccessEffect
             }
         }
    }
}
