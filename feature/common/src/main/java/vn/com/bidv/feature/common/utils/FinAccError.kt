package vn.com.bidv.feature.common.utils

import vn.com.bidv.network.NetworkStatusCode
import vn.com.bidv.sdkbase.domain.DomainResult

enum class FinAccError(val code: String) {
    ACC_LIST_TIMEOUT("ACC_LIST_TIMEOUT"),
    ACC_LIST_INQUIRY_FAIL("ACC_LIST_INQUIRY_FAIL"),
    ACC_LIST_EMPTY("ACC_LIST_EMPTY"),
    ACC_DETAIL_TIMEOUT("ACC_DETAIL_TIMEOUT"),
    ACC_DETAIL_INQUIRY_FAIL("ACC_DETAIL_INQUIRY_FAIL");

    companion object {
        fun isFinAccError(code: String?): <PERSON><PERSON>an {
            return entries.any { it.code == code }
        }
    }
}

fun DomainResult.Error.parseListAccError(): DomainResult.Error {
    return when (errorCode) {
        NetworkStatusCode.CONNECT_TIME_OUT,
        NetworkStatusCode.UNKNOWN_HOST,
        NetworkStatusCode.NO_CONNECTION -> {
            DomainResult.Error(FinAccError.ACC_LIST_TIMEOUT.code)
        }

        CommonErrCodeConstants.MD0020 -> {
            DomainResult.Error(FinAccError.ACC_LIST_INQUIRY_FAIL.code, errorMessage)
        }

        CommonErrCodeConstants.MD0003 -> {
            DomainResult.Error(FinAccError.ACC_LIST_EMPTY.code, errorMessage)
        }

        else -> this
    }
}

fun DomainResult.Error.parseAccDetailError(): DomainResult.Error {
    return when (errorCode) {
        NetworkStatusCode.CONNECT_TIME_OUT,
        NetworkStatusCode.UNKNOWN_HOST,
        NetworkStatusCode.NO_CONNECTION -> {
            DomainResult.Error(FinAccError.ACC_DETAIL_TIMEOUT.code)
        }

        CommonErrCodeConstants.MD0004 -> {
            DomainResult.Error(FinAccError.ACC_DETAIL_INQUIRY_FAIL.code, errorMessage)
        }

        else -> this
    }
}