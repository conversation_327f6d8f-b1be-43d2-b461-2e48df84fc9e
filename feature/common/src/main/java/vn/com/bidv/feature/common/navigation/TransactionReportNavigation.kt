package vn.com.bidv.feature.common.navigation

import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import androidx.navigation.navigation
import vn.com.bidv.feature.common.domain.data.TransactionRouteID
import vn.com.bidv.feature.common.ui.screen.commontransaction.TransactionBaseScreen
import vn.com.bidv.feature.common.ui.screen.commontransaction.TransactionReportViewModel
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.navigation.FeatureGraphBuilder
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.sdkbase.navigation.IBankMainRouting.TransactionReportRoute.Companion.ROUTE_ID
import javax.inject.Inject

sealed class TransactionReportRoute(val route: String) {
    data object TransactionReportMain : TransactionReportRoute("transaction_report_deeplink_route")
}

class TransactionReportNavigation @Inject constructor() : FeatureGraphBuilder {
    override fun buildGraph(
        navGraphBuilder: NavGraphBuilder,
        navController: NavHostController,
        registeredRoutes: (List<String>) -> Unit
    ) {
        navGraphBuilder.navigation(
            startDestination = TransactionReportRoute.TransactionReportMain.route,
            route = IBankMainRouting.TransactionReportRoute.TransactionReportMainRoute.routeWithArgument
        ) {
            composable(
                route = TransactionReportRoute.TransactionReportMain.route,
                arguments = listOf(navArgument(ROUTE_ID) {
                    type = NavType.StringType
                    nullable = true
                })
            ) { backStackEntry ->
                val routeId = backStackEntry.arguments?.getString(ROUTE_ID)?.let { routeID ->
                    TransactionRouteID.entries.find { it.deeplinkRouteID == routeID }
                }
                TransactionBaseScreen(
                    navController = navController,
                    actionbarTitle = stringResource(R.string.bao_cao_giao_dich),
                    viewModel = hiltViewModel<TransactionReportViewModel>(),
                    routeId = routeId
                )
            }
        }
        registeredRoutes(listOf(IBankMainRouting.TransactionReportRoute.TransactionReportMainRoute.route))
    }
}