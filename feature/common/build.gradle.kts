@Suppress("DSL_SCOPE_VIOLATION") 
plugins { 
    alias(libs.plugins.local.ibank.feature) 
    alias(libs.plugins.local.android.openapi.generate)
    alias(libs.plugins.kotlin.serialization)
} 
 
android { 
    namespace = "vn.com.bidv.feature.common" 
} 
 
dependencies { 
    implementation(libs.gson) 
    implementation(libs.swagger.annotations) 
    implementation(libs.bundles.retrofit) 
    testImplementation(libs.junit4) 
    testImplementation(libs.mockito.core) 
    testImplementation(libs.mockito.kotlin) 
    implementation(project(":core:public:designsystem"))
    implementation(libs.kotlinx.serialization.json)
    implementation(libs.core.log)
    implementation(libs.androidx.compose.ui.tooling)
    implementation(libs.google.auth)
    implementation(libs.camera.camera2)
    implementation(libs.camera.lifecycle)
    implementation(libs.camera.view)
    implementation(libs.barcode.scanning)
    implementation(libs.constraintlayout.compose)
    implementation(libs.core.secure)
    implementation(project(":feature:common:libs:ucrop"))
}
